# Geolocation Setup - @react-native-community/geolocation

## ✅ Tamamlandı

Aktivite formu artık **@react-native-community/geolocation** küt<PERSON><PERSON><PERSON><PERSON> kull<PERSON>rak kullanıcı konumunu alıyor.

## 📦 Kurulum

### 1. Package Zaten Yüklü ✅

```json
"@react-native-community/geolocation": "^3.4.0"
```

### 2. iOS için Pod Install Gerekli

iOS için native modülü yüklemek için aşağıdaki komutu çalıştırın:

```bash
cd ios
pod install
cd ..
```

### 3. Android için Ek Kurulum Gerekmez ✅

Android için gerekli izinler zaten `AndroidManifest.xml` dosyasına eklendi.

## 🔧 Yapılan Değişiklikler

### src/pages/member/dealers/sections/RenderAktiviteler.tsx

```typescript
// Import eklendi
import Geolocation from '@react-native-community/geolocation';

// getUserLocation fonksiyonu güncellendi
const getUserLocation = async () => {
    setIsGettingLocation(true);
    
    try {
        // Android için izin kontrolü
        if (Platform.OS === 'android') {
            const granted = await PermissionsAndroid.request(
                PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                {
                    title: 'Konum İzni',
                    message: 'Aktivite kaydı için konumunuza erişmek istiyoruz.',
                    buttonNeutral: 'Daha Sonra',
                    buttonNegative: 'İptal',
                    buttonPositive: 'Tamam',
                }
            );
            
            if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                Alert.alert('Uyarı', 'Konum izni verilmedi');
                setIsGettingLocation(false);
                return;
            }
        }

        // @react-native-community/geolocation kullanarak konum al
        Geolocation.getCurrentPosition(
            (position) => {
                setUserLocation({
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude,
                });
                setIsGettingLocation(false);
            },
            (error) => {
                console.log('Location error:', error);
                Alert.alert('Hata', 'Konum alınamadı. Lütfen GPS\'inizi açın.');
                setIsGettingLocation(false);
            },
            { 
                enableHighAccuracy: true,  // Yüksek doğruluk modu
                timeout: 15000,            // 15 saniye timeout
                maximumAge: 10000          // 10 saniye cache
            }
        );
    } catch (error) {
        console.log('Permission error:', error);
        setIsGettingLocation(false);
    }
};
```

## 📱 İzinler

### Android (AndroidManifest.xml) ✅

```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### iOS (Info.plist) ✅

```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Bu uygulama aktivite kaydı sırasında konumunuzu almak istiyor.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>Bu uygulama aktivite kaydı sırasında konumunuzu almak istiyor.</string>
```

## 🚀 Test Etme

### iOS

```bash
# 1. Pod install
cd ios
pod install
cd ..

# 2. iOS uygulamasını çalıştır
npm run ios
# veya
yarn ios
```

### Android

```bash
# Direkt çalıştırabilirsiniz
npm run android
# veya
yarn android
```

## 🎯 Özellikler

### Geolocation API Özellikleri

- ✅ **enableHighAccuracy: true** - GPS kullanarak yüksek doğruluk
- ✅ **timeout: 15000** - 15 saniye içinde konum alınmazsa timeout
- ✅ **maximumAge: 10000** - 10 saniye önceki konum cache'i kullanılabilir

### Position Object

```typescript
{
  coords: {
    latitude: number,        // Enlem
    longitude: number,       // Boylam
    altitude: number,        // Yükseklik (metre)
    accuracy: number,        // Doğruluk (metre)
    altitudeAccuracy: number,// Yükseklik doğruluğu
    heading: number,         // Yön (derece)
    speed: number           // Hız (m/s)
  },
  timestamp: number         // Unix timestamp
}
```

### Error Codes

```typescript
{
  PERMISSION_DENIED: 1,     // İzin reddedildi
  POSITION_UNAVAILABLE: 2,  // Konum alınamadı
  TIMEOUT: 3               // Zaman aşımı
}
```

## 🐛 Sorun Giderme

### iOS'ta Konum Alınamıyor

1. **Pod install yaptınız mı?**
   ```bash
   cd ios
   pod install
   cd ..
   ```

2. **Info.plist'te izinler var mı?**
   - `NSLocationWhenInUseUsageDescription` kontrolü

3. **Simulator'da konum simülasyonu**
   - Xcode > Debug > Simulate Location > Custom Location

### Android'de Konum Alınamıyor

1. **GPS açık mı?**
   - Ayarlar > Konum > GPS açık

2. **İzinler verildi mi?**
   - Ayarlar > Uygulamalar > ttsYoda > İzinler > Konum

3. **Emulator'da konum simülasyonu**
   - Extended Controls > Location > Set location

## 📊 Kullanım Örneği

```typescript
// Modal açıldığında otomatik konum al
const openModal = () => {
    setModalVisible(true);
    getUserLocation(); // Otomatik konum alma
    // ... animasyonlar
};

// Kaydetme sırasında konum verisi
const handleSave = async () => {
    const postData = {
        activityType: 'ziyaret',
        activityNotes: 'Bayi ziyareti...',
        dealer: { ... },
        user: { ... },
        userLocation: {
            latitude: 41.0082,
            longitude: 28.9784
        },
        timestamp: '2025-09-29T10:30:00.000Z'
    };
    
    // API'ye gönder
    console.log('Activity saved:', postData);
};
```

## 🔗 Kaynaklar

- [react-native-community/geolocation GitHub](https://github.com/react-native-community/geolocation)
- [API Documentation](https://github.com/react-native-community/geolocation#api)
- [Geolocation Web API](https://developer.mozilla.org/en-US/docs/Web/API/Geolocation_API)

## ✅ Checklist

- [x] Package yüklü (`@react-native-community/geolocation`)
- [x] Import eklendi
- [x] `getUserLocation()` fonksiyonu güncellendi
- [x] Android izinleri eklendi
- [x] iOS izinleri eklendi
- [ ] **iOS pod install yapılmalı** ⚠️
- [ ] Gerçek cihazda test edilmeli

## 🎉 Sonuç

Aktivite formu artık **@react-native-community/geolocation** kullanarak profesyonel bir şekilde kullanıcı konumunu alıyor!

**Önemli**: iOS için `pod install` yapmayı unutmayın!

