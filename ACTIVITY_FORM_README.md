# Activity Entry Form - Aktivite Giriş Formu

## 📋 Özellikler

Modern ve estetik bir aktivite giriş formu tasarlandı. Form aşağıdaki özelliklere sahiptir:

### ✨ Temel Özellikler

1. **Aktivite Türü Seçimi**
   - <PERSON><PERSON><PERSON> (map-pin ikonu)
   - Telefon (phone ikonu)
   - Email (mail ikonu)
   - Modern, görsel buton tasarımı ile seçim

2. **Aktivite Notları**
   - Çok satırlı metin alanı
   - Placeholder ile kullanıcı dostu
   - Otomatik büyüyen textarea

3. **Fotoğraf Yönet<PERSON>i** (Sadece Ziyaret türü için)
   - 📷 **Kamera ile Fotoğraf Çekme**
     - Doğrudan kamera açılır
     - Çekilen fotoğraf otomatik eklenir
   - 🖼️ **Galeriden Fotoğraf Seçme**
     - Çoklu fotoğraf seçim<PERSON> (max 10)
     - <PERSON><PERSON> er<PERSON>imi
   - 🗑️ **Fotoğra<PERSON>**
     - Her fotoğrafın üzerinde sil butonu
     - Tek tıkla silme

4. **Konum Bilgisi** 📍
   - Modal açıldığında otomatik konum alınır
   - GPS koordinatları (latitude, longitude)
   - Konum alınamazsa "Tekrar Dene" butonu
   - Konum izni yönetimi (Android & iOS)

5. **Kullanıcı ve Bayi Bilgileri**
   - Kullanıcı bilgileri otomatik eklenir (id, name, email)
   - Bayi bilgileri otomatik eklenir (id, unvan, kod, adres)
   - Timestamp (kayıt zamanı)

6. **Form Validasyonu**
   - Aktivite notları zorunlu alan kontrolü
   - Kullanıcı dostu uyarı mesajları

7. **Animasyonlu Modal**
   - Smooth açılış/kapanış animasyonları
   - Loading overlay ile kaydetme durumu gösterimi
   - Backdrop ile modal dışı tıklama ile kapatma

## 🎨 Tasarım Özellikleri

- **Renk Paleti**: #6B4EFF (Primary Purple)
- **Modern Card Design**: Rounded corners, shadows
- **Responsive Layout**: Mobil cihazlara optimize
- **Icon System**: Lucide icons kullanımı
- **Typography**: Hiyerarşik font boyutları

## 📱 Kullanım

### Form Açma
1. Bayi detay sayfasında "Aktivite ve Ziyaretler" kartına gidin
2. Sağ üstteki "Yeni" butonuna tıklayın

### Aktivite Ekleme
1. **Aktivite türünü seçin**: Ziyaret, Telefon veya Email
2. **Notları girin**: Aktivite detaylarını yazın
3. **Fotoğraf ekleyin** (Ziyaret türü için):
   - "Fotoğraf Çek" ile kamera açın
   - "Galeriden Seç" ile mevcut fotoğrafları seçin
   - İstenmeyen fotoğrafları sil butonu ile kaldırın
4. **Kaydet** butonuna tıklayın

### Form İptal Etme
- "İptal" butonu veya modal dışına tıklayarak formu kapatabilirsiniz
- Form kapandığında tüm veriler sıfırlanır

## 🔧 Teknik Detaylar

### Kullanılan Kütüphaneler
- `react-native-image-picker`: Kamera ve galeri erişimi
- `react-native-paper`: Material Design bileşenleri
- `@react-native-vector-icons/lucide`: Modern ikonlar

### İzinler

#### Android (AndroidManifest.xml)

```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

#### iOS (Info.plist)

```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Bu uygulama aktivite kaydı sırasında konumunuzu almak istiyor.</string>
<key>NSLocationAlwaysUsageDescription</key>
<string>Bu uygulama aktivite kaydı sırasında konumunuzu almak istiyor.</string>
<key>NSCameraUsageDescription</key>
<string>Bu uygulama aktivite fotoğrafları çekmek için kameranıza erişmek istiyor.</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>Bu uygulama aktivite fotoğrafları eklemek için foto galerinize erişmek istiyor.</string>
<key>NSPhotoLibraryAddUsageDescription</key>
<string>Bu uygulama çekilen fotoğrafları kaydetmek için foto galerinize erişmek istiyor.</string>
```

## 🚀 Test Etme

### iOS Simulator
```bash
npm run ios
# veya
yarn ios
```

**Not**: iOS simulator'da kamera çalışmaz, sadece galeri erişimi test edilebilir.

### Android Emulator
```bash
npm run android
# veya
yarn android
```

**Not**: Android emulator'da kamera emülasyonu çalışır.

### Gerçek Cihaz (Önerilen)
Kamera özelliğini tam olarak test etmek için gerçek cihaz kullanmanız önerilir.

## 📸 Ekran Görüntüleri

Form aşağıdaki bölümlerden oluşur:

1. **Modal Header**: Başlık, alt başlık ve kapat butonu
2. **Aktivite Türü Seçici**: 3 görsel buton (Ziyaret, Telefon, Email)
3. **Notlar Alanı**: Çok satırlı metin girişi
4. **Fotoğraf Bölümü**: Kamera ve galeri butonları + fotoğraf grid'i
5. **Modal Footer**: İptal ve Kaydet butonları

## 🔄 Veri Akışı

1. Modal açılır
2. **Otomatik konum alınır** (GPS)
3. Kullanıcı formu doldurur
4. "Kaydet" butonuna tıklar
5. Form validasyonu yapılır
6. **Veri paketi oluşturulur**:
   ```javascript
   {
     activityType: 'ziyaret',
     activityNotes: 'Bayi ziyareti yapıldı...',
     images: [...],
     dealer: {
       id: '123',
       unvan: 'ABC Turizm',
       kod: 'ABC001',
       sehir: 'İstanbul',
       ilce: 'Kadıköy',
       adres: 'Tam adres...'
     },
     user: {
       id: 'user123',
       name: 'Ahmet Yılmaz',
       email: '<EMAIL>'
     },
     userLocation: {
       latitude: 41.0082,
       longitude: 28.9784
     },
     timestamp: '2025-09-29T10:30:00.000Z'
   }
   ```
7. Loading overlay gösterilir
8. 2 saniye simüle edilmiş server isteği
9. Başarılı olursa:
   - Form sıfırlanır
   - Modal kapanır
   - İçerik yenilenir (refreshContent callback)
10. Hata olursa:
    - Alert gösterilir
    - Form açık kalır

## 🎯 Gelecek Geliştirmeler

- [ ] Tarih/saat seçici ekleme
- [ ] Konum bilgisi ekleme (GPS)
- [ ] Fotoğraf önizleme modal'ı
- [ ] Fotoğraf düzenleme (crop, rotate)
- [ ] Offline destek
- [ ] Draft kaydetme
- [ ] Aktivite geçmişi listeleme

## 📝 Notlar

- Form simüle edilmiş bir server isteği kullanır (2 saniye delay)
- Gerçek API entegrasyonu için `handleSave` fonksiyonunu güncelleyin
- Fotoğraflar base64 veya FormData olarak sunucuya gönderilebilir
- Maximum 10 fotoğraf seçilebilir (ayarlanabilir)

## 🐛 Bilinen Sorunlar

- iOS simulator'da kamera çalışmaz (gerçek cihazda çalışır)
- Android 13+ için READ_MEDIA_IMAGES izni gereklidir

## 📞 Destek

Herhangi bir sorun veya soru için lütfen iletişime geçin.

