import { StyleSheet, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

export const stylex = {
    RenderContactPeople: {
        // Contact Cards Styles
        comingSoon: {
            textAlign: 'center',
            color: '#666',
            fontStyle: 'italic',
        },
        contactCardsContainer: {
            marginVertical: 2,
            paddingHorizontal: 2,
        },
        sectionTitle: {
            fontSize: 18,
            fontWeight: '600',
            color: '#2D3748',
            marginBottom: 12,
        },
        contactCard: {
            backgroundColor: '#FFFFFF',
            borderRadius: 6,
            borderWidth: 0.5,
            borderColor: '#cccccc',
            padding: 6,
            marginBottom: 6,
            // shadowColor: '#000',
            // shadowOffset: { width: 0, height: 1 },
            // shadowOpacity: 0.1,
            // shadowRadius: 4,
            // elevation: 3,
        },
        contactCardHeader: {
            flexDirection: 'row',
            alignItems: 'center',
            borderBottomWidth: 1,
            borderBottomColor: '#EDF2F7',
            paddingBottom: 12,
            marginBottom: 12,
        },
        headerTextContainer: {
            marginLeft: 0,
            paddingVertical: 8,
        },
        nameText: {
            fontSize: 16,
            fontWeight: '500',
            color: '#2D3748',
        },
        positionText: {
            fontSize: 12,
            color: '#718096',
            marginTop: 0,
        },
        infoContainer: {
            marginTop: 12,
        },
        infoRow: {
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
        },
        infoText: {
            fontSize: 14,
            color: '#4A5568',
            marginLeft: 8,
        },
        cardFooter: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            borderTopWidth: 1,
            borderTopColor: '#EDF2F7',
            paddingTop: 12,
            marginTop: 12,
        },
        dateContainer: {
            flexDirection: 'row',
            alignItems: 'center',
        },
        dateText: {
            fontSize: 12,
            color: '#718096',
            marginLeft: 6,
        },
        // Existing styles below
        card: {
            backgroundColor: 'white',
            borderRadius: 12,
            marginBottom: 16,
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 2 },
            shadowOpacity: 0.1,
            shadowRadius: 4,
            elevation: 3,
        },
        cardHeader: {
            flexDirection: 'row',
            alignItems: 'center',
            padding: 16,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
        },
        cardTitle: {
            fontSize: 14,
            fontWeight: '600',
            color: '#333',
            marginLeft: 12,
        },
        cardContent: {
            padding: 16,
        },
        contactInfoRow: {
            flexDirection: 'row',
            alignItems: 'flex-start',
            marginBottom: 12,
        },
        infoLabel: {
            fontSize: 14,
            color: '#666',
            marginLeft: 12,
            flex: 1,
        },
        infoValue: {
            fontSize: 14,
            color: '#333',
            fontWeight: '500',
            flex: 2,
            textAlign: 'right',
        },
        addButton: {
            flexDirection: 'row',
            alignItems: 'center',
            gap: 4,
            paddingVertical: 6,
            paddingHorizontal: 12,
            borderRadius: 20,
            backgroundColor: '#F0EDFF',
        },
        addButtonText: {
            fontSize: 12,
            color: '#6B4EFF',
            fontWeight: '500',
        },
        modalOverlay: {
            flex: 1,
            backgroundColor: 'transparent',
        },
        modalBackdrop: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.25)',
        },
        keyboardAvoidingView: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
        },
        modalContainer: {
            width: '95%',
            maxWidth: 450,
            height: '80%',
            backgroundColor: 'white',
            borderRadius: 20,
            overflow: 'hidden',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.25,
            shadowRadius: 20,
            elevation: 12,
        },
        modalHeader: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: 24,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
            backgroundColor: '#FAFBFF',
        },
        modalHeaderContent: {
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
        },
        modalIconContainer: {
            width: 36,
            height: 36,
            borderRadius: 18,
            backgroundColor: '#F0EDFF',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
        },
        modalTitleContainer: {
            flex: 1,
        },
        modalTitle: {
            fontSize: 16,
            fontWeight: '700',
            color: '#1A1A1A',
            marginBottom: 4,
        },
        modalSubtitle: {
            fontSize: 12,
            color: '#666',
            fontWeight: '400',
        },
        closeButton: {
            width: 36,
            height: 36,
            borderRadius: 18,
            backgroundColor: '#F5F5F5',
            justifyContent: 'center',
            alignItems: 'center',
        },
        modalScrollView: {
            flex: 1,
            backgroundColor: 'white',
        },
        modalContent: {
            padding: 16,
            paddingTop: 16,
        },
        inputGroup: {
            marginBottom: 20,
        },
        inputLabel: {
            fontSize: 12,
            fontWeight: '600',
            color: '#1A1A1A',
            marginBottom: 2,
            marginLeft: 4,
        },
        inputContainer: {
            position: 'relative',
        },
        inputIcon: {
            position: 'absolute',
            left: 8,
            top: 14,
            zIndex: 1,
        },
        input: {
            paddingLeft: 14,
            paddingVertical: 5,
            fontSize: 14,
            backgroundColor: '#FAFAFA',
            paddingBottom: 5,
            padding: 0,
            height: 30,

            // borderWidth: 1,

        },
        checkboxContainer: {
            flexDirection: 'row',
            alignItems: 'center',
            marginBottom: 8,
            marginTop: 8,
            backgroundColor: '#F8F9FF',
            padding: 4,
            borderRadius: 12,
            borderWidth: 1,
            borderColor: '#E8E5FF',
        },
        checkboxLabel: {
            fontSize: 15,
            color: '#333',
            marginLeft: 12,
            flex: 1,
            fontWeight: '500',
        },
        checkboxIcon: {
            marginLeft: 8,
        },
        modalActions: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            padding: 16,
            gap: 12,
            borderTopWidth: 1,
            borderTopColor: '#F0F0F0',
            backgroundColor: '#FAFBFF',
        },
        actionButton: {
            minWidth: 40,
            borderRadius: 10,
            paddingVertical: 2,
        },
        cancelButton: {
            borderColor: '#E0E0E0',
            backgroundColor: 'transparent',
        },
        cancelButtonText: {
            color: '#666',
            fontSize: 14,
            fontWeight: '600',
            marginHorizontal: 8,
            marginVertical: 6,
        },
        saveButton: {
            backgroundColor: '#6B4EFF',
            elevation: 2,
            shadowColor: '#6B4EFF',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
        },
        saveButtonText: {
            color: 'white',
            fontSize: 14,
            fontWeight: '700',
            marginHorizontal: 8,
            marginVertical: 6,

        },
    },
}