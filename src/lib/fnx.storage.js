import {MMKV} from 'react-native-mmkv';
const {Keys} = require('../constants/index');

const storage = new MMKV();
const storageKey = '@yodaLocalStorage';
export const avgLifeTimeDays = 2;

export const dbLocalTables = {
  entity_dealer: 'repo_entityDealer',
  entity_dealers: 'repo_entityDealers',
  entity_dealers_vars: 'repo_entityDealers_vars',

  entity_dealer_prospect: 'repo_entityDealerProspect',
  entity_dealer_prospects: 'repo_entityDealerProspects',
  entity_dealer_prospects_vars: 'repo_entityDealerProspects_vars',
};

export const AsyncStorage = {
  getItem: key => {
    return new Promise(async resolve => {
      try {
        var value = storage.getString(key);
      resolve(value);
      } catch (error) {
        console.error('getItem error', error);
        resolve(null);
      }
    });
  },
  getItemLimited: (key, options = {}) => {
    const {getAll = false, deleteObselete = true, getCustom = false} = options;
    return new Promise(async (resolve, reject) => {
      var storeKeyValue = key;
      try {
        const dataKey = storageKey + ':' + storeKeyValue;
        var value = await AsyncStorage.getItem(dataKey);
        if (value !== null && value !== undefined) {
          var timeDiff = Date.now() - parseInt(JSON.parse(value).EOP, 10);
          if (timeDiff < 0) {
            if (!getCustom) {
              var data = !getAll ? JSON.parse(value).Data : JSON.parse(value);
            } else {
              var data = JSON.parse(value)[getCustom];
            }
            resolve(data);
          } else {
            deleteObselete && storage.delete(dataKey);
            reject({result: false, code: 'bayat'});
          }
        } else {
          reject({result: false, code: 'nostoragedata'});
        }
      } catch (error) {
        console.error('getItemLimited error', error);
        reject({result: false, code: 'critic'});
      }
    });
  },
  setItem: (key, value) => {
    return new Promise(async resolve => {
      storage.set(key, value);
      resolve(true);
    });
  },
  setItemLimited: (key, value, sureGun = 2) => {
    return new Promise(async resolve => {
      function setStoreData(VeriAdi, Veri, SureGun) {
        var StoreData = {};
        var result = new Date();
        StoreData['BOP'] = Date.now();
        StoreData['EOP'] = result.setDate(result.getDate() + SureGun);
        StoreData['SureGun'] = SureGun;
        StoreData['DataName'] = VeriAdi;
        StoreData['Data'] = Veri;
        //console.log('storeData', JSON.stringify(StoreData))
        return StoreData;
      }
      try {
        let dataKey = storageKey + ':' + key;
        storage.delete(dataKey);
        let data2Store = JSON.stringify(setStoreData(key, value, sureGun));
        storage.set(dataKey, data2Store);
        resolve(true);
      } catch (e) {
        console.error('setItemLimited fn error', e);
        resolve(false);
      }
    });
  },
  setItemLimitedwSec: (key, value, sureSn = 60) => {
    return new Promise(async resolve => {
      function setStoreData(VeriAdi, Veri, sure) {
        var StoreData = {};
        var result = Date.now();
        var sureJS = sure * 1000;
        StoreData['BOP'] = result;
        StoreData['EOP'] = result + sureJS;
        StoreData['SureGun'] = sure;
        StoreData['DataName'] = VeriAdi;
        StoreData['Data'] = Veri;
        //console.log('storeData', JSON.stringify(StoreData))
        return StoreData;
      }
      try {
        let dataKey = storageKey + ':' + key;
        storage.delete(dataKey);
        let data2Store = JSON.stringify(setStoreData(key, value, sureSn));
        // console.log('data2Store', data2Store, dataKey)
        storage.set(dataKey, data2Store);
        resolve(true);
      } catch (e) {
        console.error('setItemLimitedwSec fn error', e);
        resolve(false);
      }
    });
  },
  removeItem: key => {
    return new Promise(async resolve => {
      storage.delete(key);
      resolve(true);
    });
  },
  removeItemLimited: key => {
    let dataKey = storageKey + ':' + key;
    return new Promise(async resolve => {
      storage.delete(dataKey);
      resolve(true);
    });
  },
  getAll: () => {
    return new Promise(async resolve => {
      const keys = storage.getAllKeys();
      resolve(keys);
    });
  },
  removeItemContainsLimited: async (key = 'xxxx', debug = false) => {
    let dataKey = storageKey + ':' + key;
    return new Promise(async resolve => {
      try {
        const keys = await AsyncStorage.getAll(); // storage.getAllKeys();
        debug && console.log('keys', JSON.stringify(keys), dataKey);
        let filteredKeys = keys.filter(function (str) {
          return str.indexOf(dataKey) !== -1;
        });
        debug &&
          Array.isArray(filteredKeys) &&
          console.log('items 2 delete', filteredKeys.length);
        Array.isArray(filteredKeys) &&
          filteredKeys.length !== 0 &&
          filteredKeys.map(async k => {
            await AsyncStorage.removeItem(k);
          });
        // storage.delete(dataKey);
        resolve(true);
      } catch (e) {
        resolve(false);
      }
    });
  },
  clearAll: () => {
    return new Promise(async resolve => {
      //const keys = storage.getAllKeys();
      storage.clearAll();
      console.log('cleared all storage!');
      resolve(true);
    });
  }, 
  //const keys = storage.getAllKeys() storage.clearAll()
};
