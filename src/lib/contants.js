import {Platform} from 'react-native';

const localMode = true;
const ipAddress = Platform.OS === 'android' ? '********' : '127.0.0.1';  

export const appvars = {
    appname : 'YODA',
    localMode: localMode,
    backendhost: localMode ? ipAddress : 'yoda.subanet.com',
    backendprotocol: localMode ? 'http' : 'https',
    port: localMode ? '3000' : '',
    frontendhost: localMode ? ipAddress : 'yoda.subanet.com',
    frontendprotocol: localMode ? 'http' : 'https',
    frontendport: localMode ? '3000' : '',
    appversion: '0.0.1',
    appbuild: '1', 
    backendUri: localMode ? `http://${ipAddress}:3000` : `https://yoda.subanet.com`,
}

export const appModules = {
    bayi: {
        name: 'Dealers',
        description: 'Dealer Management',
        moduleName: 'bayi',
        navigationName: 'DealersHome',
        icon: {
            name: 'refrigerator',
            type: 'font-awesome',
            size: 28,
            color: '#517fa4',
        },
        route: 'Bayi',
    },
    satis: {
        name: '<PERSON><PERSON>',
        icon: 'shopping-cart',
        route: 'Satis',
    }
}

export const apiurls = {
    getvars: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/tenant/variables/`,
    login: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/auth/mobile`,
    
    getdealers: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/list`,
    getdealer: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/`,
    getdealers_contacts: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/contacts`,
    getdealers_activities: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/activities`,
    
    getdealerprospects: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/prospects`,
    // getdealerprospects_contacts: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/api/entity/dealers/contacts?refData=adayBayi`,
    // signup: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/signup`,
    // verifytoken: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/verifytoken`,
    // changepassword: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/changepassword`,
    // resetpassword: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/resetpassword`,
    // getuserdetails: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/getuserdetails`,
    // updateuserdetails: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/updateuserdetails`,
    // getnotes: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/getnotes`,
    // addnote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/addnote`,
    // updatenote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/updatenote`,
    // deletenote: `${appvars.backendprotocol}://${appvars.backendhost}${appvars.port ? ':' + appvars.port : ''}/deletenote`,
};
