
import { Platform, Dimensions } from 'react-native';

const { width } = Dimensions.get('window');

export const stylex = {

    activityDate: {
        fontSize: 12,
        color: '#666',
        marginLeft: 'auto',
    },
    mediaContainer: {
        marginTop: 8,
    },
    mediaThumbnail: {
        width: 80,
        height: 80,
        borderRadius: 8,
        marginRight: 8,
    },
    showMoreButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 8,
        padding: 12,
        backgroundColor: '#F8F9FF',
        borderRadius: 8,
        marginTop: 16,
        borderWidth: 1,
        borderColor: '#E8E5FF',
    },
    showMoreText: {
        color: '#6B4EFF',
        fontSize: 14,
        fontWeight: '500',
    },

    // Orijinal stiller (yinelenenler kaldırıldı)
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
        minHeight: 100,
        justifyContent: 'center',
    },
    comingSoon: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
    },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 20,
        backgroundColor: '#F0EDFF',
    },
    addButtonText: {
        fontSize: 12,
        color: '#6B4EFF',
        fontWeight: '500',
    },
    activityCardsContainer: {
        gap: 12,
    },
    activityCard: {
        backgroundColor: '#FAFAFA',
        borderRadius: 12,
        padding: 16,
    },
    activityType: {
        fontSize: 14,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    activityNotes: {
        fontSize: 12,
        color: '#666',
        marginTop: 8,
    },
    // Modal stilleri
    modalOverlay: {
        flex: 1,
        backgroundColor: 'transparent',
    },
    modalBackdrop: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.25)',
    },
    keyboardAvoidingView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContainer: {
        width: '95%',
        maxWidth: 450,
        height: '80%',
        backgroundColor: 'white',
        borderRadius: 20,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
        elevation: 12,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 24,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    modalHeaderContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    modalIconContainer: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#F0EDFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    modalTitleContainer: {
        flex: 1,
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: '700',
        color: '#1A1A1A',
        marginBottom: 4,
    },
    modalSubtitle: {
        fontSize: 12,
        color: '#666',
        fontWeight: '400',
    },
    closeButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#F5F5F5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalScrollView: {
        flex: 1,
        backgroundColor: 'white',
    },
    modalContent: {
        padding: 16,
        paddingTop: 16,
    },
    inputGroup: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 12,
        fontWeight: '600',
        color: '#1A1A1A',
        marginBottom: 8,
        marginLeft: 4,
    },
    typeSelector: {
        flexDirection: 'row',
        gap: 8,
        justifyContent: 'space-between',
    },
    typeOption: {
        flex: 1,
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        paddingHorizontal: 12,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#E0E0E0',
        backgroundColor: '#FAFAFA',
        gap: 8,
    },
    typeOptionActive: {
        borderColor: '#6B4EFF',
        backgroundColor: '#F0EDFF',
    },
    typeOptionText: {
        fontSize: 13,
        fontWeight: '600',
        color: '#666',
    },
    typeOptionTextActive: {
        color: '#6B4EFF',
    },
    textAreaContainer: {
        position: 'relative',
    },
    textArea: {
        fontSize: 14,
        backgroundColor: '#FAFAFA',
        minHeight: 120,
        textAlignVertical: 'top',
    },
    locationInfo: {
        marginTop: 8,
        padding: 12,
        backgroundColor: '#F8F9FF',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#E8E5FF',
    },
    locationHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
    },
    locationTitle: {
        fontSize: 13,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    locationLoading: {
        paddingVertical: 8,
    },
    locationLoadingText: {
        fontSize: 12,
        color: '#666',
        fontStyle: 'italic',
    },
    locationDetails: {
        paddingVertical: 4,
    },
    locationText: {
        fontSize: 12,
        color: '#333',
        fontFamily: Platform.OS === 'ios' ? 'Courier' : 'monospace',
    },
    locationError: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingVertical: 4,
    },
    locationErrorText: {
        fontSize: 12,
        color: '#999',
        fontStyle: 'italic',
    },
    retryButton: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        backgroundColor: '#6B4EFF',
        borderRadius: 6,
    },
    retryButtonText: {
        fontSize: 11,
        color: '#fff',
        fontWeight: '600',
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 16,
        gap: 12,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    actionButton: {
        minWidth: 40,
        borderRadius: 10,
        paddingVertical: 2,
    },
    cancelButton: {
        borderColor: '#E0E0E0',
        backgroundColor: 'transparent',
    },
    cancelButtonText: {
        color: '#666',
        fontSize: 14,
        fontWeight: '600',
        marginHorizontal: 8,
        marginVertical: 6,
    },
    saveButton: {
        backgroundColor: '#6B4EFF',
        elevation: 2,
        shadowColor: '#6B4EFF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
    },
    saveButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '700',
        marginHorizontal: 8,
        marginVertical: 6,
    },
    detailButton: {
        position: 'absolute',
        bottom: 8,
        right: 8,
        padding: 4,
    },
    photoActions: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 16,
    },
    photoActionButton: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#E0E0E0',
        borderStyle: 'dashed',
        backgroundColor: '#FAFBFF',
        gap: 8,
    },
    photoActionIcon: {
        width: 48,
        height: 48,
        borderRadius: 24,
        backgroundColor: '#F0EDFF',
        justifyContent: 'center',
        alignItems: 'center',
    },
    photoActionText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    imagesGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
        marginTop: 8,
    },
    imageContainer: {
        position: 'relative',
        width: 80,
        height: 80,
        borderRadius: 12,
        overflow: 'hidden',
        backgroundColor: '#F0F0F0',
    },
    imageThumb: {
        width: '100%',
        height: '100%',
    },
    removeImageButton: {
        position: 'absolute',
        top: 4,
        right: 4,
        width: 24,
        height: 24,
        borderRadius: 12,
        backgroundColor: 'rgba(255, 0, 0, 0.8)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageModalContainer: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    imageModalContent: {
        width: '100%',
        height: '100%',
    },
    closeButtonImageModal: {
        position: 'absolute',
        top: 50,
        right: 20,
        zIndex: 10,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        borderRadius: 18,
        padding: 8,
    }
};