import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GoogleSignin } from '@react-native-google-signin/google-signin';

import { AsyncStorage as fnStorage } from '../lib/fnx.storage';

// Type definitions
export type ModulePermission = {
  module: string;
  moduleTitle: string;
  menuWeight?: number;
  menuIcon?: string;
};

export type PositionRole = {
  permissionDetails?: ModulePermission[];
  // Add other properties as needed
};

export type UserInfo = {
  user: {
    id: string;
    name: string | null;
    email: string | null;
    photo: string | null;
    positionsAndRoles: PositionRole[] | null;
  };
  scopes: string[];
  token: string | null;
  refreshToken?: string | null;
  serverAuthCode: string | null;
};

type UserContextType = {
  userInfo: UserInfo | null;
  currentPage: 'landing' | 'login' | 'secure';
  setCurrentPage: React.Dispatch<React.SetStateAction<'landing' | 'login' | 'secure'>>;
  isLoading: boolean;
  handleLoginSuccess: (userData: UserInfo) => Promise<void>;
  handleLogout: (navigation?: any) => Promise<void>;
  showNotification?: (message: string, type: 'error' | 'success') => void;
  userModules?: (object | null)[];
};

// Create the context
const UserContext = createContext<UserContextType | undefined>(undefined);

// Provider component
export const UserProvider: React.FC<{ children: ReactNode; showNotification?: (message: string, type: 'error' | 'success') => void }> = ({ children, showNotification }) => {
  const [currentPage, setCurrentPage] = useState<'landing' | 'login' | 'secure'>('landing');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check cached session on app start
  const checkCachedSession = async () => {
    try {
      const cachedUserInfo = await AsyncStorage.getItem('userInfo');
      const loginTime = await AsyncStorage.getItem('loginTime');
      
      if (cachedUserInfo && loginTime) {
        const currentTime = new Date().getTime();
        const timeDifference = currentTime - parseInt(loginTime);
        const ninetyMinutesInMs = 90 * 60 * 1000; // 90 minutes in milliseconds
        
        if (timeDifference < ninetyMinutesInMs) {
          // Session is still valid
          setUserInfo(JSON.parse(cachedUserInfo));
          setCurrentPage('secure');
        } else {
          // Session expired, clear cache
          await AsyncStorage.removeItem('userInfo');
          await AsyncStorage.removeItem('loginTime');
        }
      }
    } catch (error) {
      console.error('Error checking cached session:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginSuccess = async (userData: UserInfo) => {
    setUserInfo(userData);
    setCurrentPage('secure');
    
    // Cache user info and login time
    try {
      await AsyncStorage.setItem('userInfo', JSON.stringify(userData));
      await AsyncStorage.setItem('loginTime', new Date().getTime().toString());
      console.log('User session cached.', userData);
    } catch (error) {
      console.error('Error caching user session:', error);
    }
  };
  const UserModules = (user: UserInfo | null) => {
    const uniqueModules = new Map(); // module -> module details eşlemesi için

    if (!user || !user.user.positionsAndRoles || !Array.isArray(user.user.positionsAndRoles)) {
      return []; // Güvenli dönüş
    }

    // Since positionsAndRoles is string[], we map each position to a module
    // Adjust this logic based on your actual data structure and requirements
    user.user.positionsAndRoles.forEach(position => {

      if (position.permissionDetails && Array.isArray(position.permissionDetails)) {
        position.permissionDetails.forEach(detail => {
          // console.log('detail', detail);
          const { module, moduleTitle, menuWeight, menuIcon  } = detail;
          if (module && !uniqueModules.has(module)) {
            uniqueModules.set(module, { module, moduleTitle, menuWeight, menuIcon });
          }
        });
      }
      // if (position) {
      //   // For now, we're using the position as both module and moduleTitle
      //   // You should replace this with your actual mapping logic
      //   uniqueModules.set(position, position);
      // }
    });

    // Map'i obje dizisine çevir
    return Array.from(uniqueModules.values());
  };


  const handleLogout = async (navigation?: any) => {
    try {
      await GoogleSignin.signOut();
      setUserInfo(null);
      setCurrentPage('landing');
      
      // Clear cached session
      await AsyncStorage.removeItem('userInfo');
      await AsyncStorage.removeItem('loginTime');
      
      try {
        fnStorage.clearAll();
      } catch (e) {
        console.error('Error clearing all storage:', e);
      }

      // Navigate directly to LandingPage
      if (navigation) {
        navigation.reset({
          index: 0,
          routes: [{ name: 'Landing' }],
        });
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  useEffect(() => {
    checkCachedSession();
  }, []);

  return (
    <UserContext.Provider
      value={{
        userInfo,
        currentPage,
        setCurrentPage,
        isLoading,
        handleLoginSuccess,
        handleLogout,
        showNotification,
        userModules: UserModules(userInfo),
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

// Custom hook to use the context
export const useUser = () => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
