
import Lucide from '@react-native-vector-icons/lucide';
import React from 'react';
import {
    Platform,
    TouchableWithoutFeedback,
    KeyboardAvoidingView,
    View, Text, StyleSheet,
    ScrollView,
    TouchableOpacity, Modal, Animated, Easing
} from 'react-native';

import { TextInput, Button } from 'react-native-paper';

import { useUser } from '../../../contexts/UserContext.tsx';
import { apiurls } from '../../../lib/contants.js';
import RefreshOverlay from './List.RefreshOverlay.tsx';

const ProspectsAddNew = (props: any) => {
    const { userInfo } = useUser();

    // Validation functions
    const isValidEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email.trim());
    };

    const isValidPhone = (phone: string): boolean => {
        const phoneRegex = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/;
        return phoneRegex.test(phone.trim().replace(/\s+/g, ''));
    };

    const [modalVisible, setModalVisible] = React.useState(false);
    const [isSaving, _setIsSaving] = React.useState(false);
    const [isDeleting, _setIsDeleting] = React.useState(false);
    const [isEditing, _setIsEditing] = React.useState(false);

    const [phone, setPhone] = React.useState('905551234567');
    const [selectedSegment, setSelectedSegment] = React.useState('bayi');
    const segments = ['bayi', 'sistem', 'alternatif'];
    const [ticariUnvan, setTicariUnvan] = React.useState('Ticari Unvan');
    const [selectedSources, setSelectedSources] = React.useState<string[]>(['web']);
    const sources = ['sistem', 'web', 'reference'];
    const [acm, _setAcm] = React.useState<string>('');
    const [konum, setKonum] = React.useState({
        sehir: 'istanbul',
        ilce: 'kadikoy',
        adres: 'ornek adres girisi'
    });
    const [iletisim, setIletisim] = React.useState({
        webSitesi: '',
        cep: '905551234567',
        email: '<EMAIL>'
    });
    const [kontakKisi, setKontakKisi] = React.useState({
        fullName: 'Kontak Kisi',
        email: '<EMAIL>',
        phone: '905551234567'
    });

    const [tabelaAdi, settabelaAdi] = React.useState('Tabela Adi AS');
    const [_isAuthorized, _setIsAuthorized] = React.useState(false);

    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(1)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(1)).current;


    // Handle modal open with animation
    const openModal = () => {
        // console.log('Opening modal...', dealer);
        // Set initial animation values for entrance
        modalScaleAnim.setValue(0.8);
        modalOpacityAnim.setValue(0);

        setModalVisible(true);
        // console.log('Modal visible set to true');

        // Start entrance animation immediately
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            })
        ]).start(() => {
            console.log('Animation completed');
        });
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setModalVisible(false);
            // Reset form fields
        });
    };

    const handleCancel = () => {
        closeModal();
    };
    const handleSave = async () => {
        const newProspect = {
            segment: selectedSegment,
            tabelaAdi,
            ticariUnvan,
            source: selectedSources, 
            konum,
            iletisim,
            kontakKisi
        };

        console.log('API Request Data:', JSON.stringify(newProspect, null, 2));
        
        const uri = apiurls.getdealerprospects;
            
        // Simulate API call
        try {
            // Here would be the actual API call:
            // await axios.post('/api/prospects', newProspect);

            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
                body: JSON.stringify(newProspect),
            });
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }
            const data = await res.json();
            // console.log('Contact saved', data);
            // Close modal after saving
            closeModal();
            props.refreshContent && props.refreshContent();


            console.log('Prospect successfully submitted');
        } catch (error) {
            console.error('Error submitting prospect:', error);
        } finally {
            // closeModal();
        }
    };

    const refreshOverlayOpacity = React.useRef(new Animated.Value(0)).current;
    const refreshOverlayScale = React.useRef(new Animated.Value(0.8)).current;
    const spinValue = React.useRef(new Animated.Value(0)).current;

    // Computed form validation
    const isFormValid = React.useMemo(() => {
        const hasRequiredFields = 
            tabelaAdi.trim() !== '' &&
            konum.sehir.trim() !== '' &&
            konum.ilce.trim() !== '' &&
            kontakKisi.fullName.trim() !== '' &&
            phone.trim() !== '' &&
            iletisim.email.trim() !== '';

        const hasValidEmail = isValidEmail(iletisim.email);
        const hasValidPhone = isValidPhone(phone);

        console.log('Form Validation:', {
            tabelaAdi: tabelaAdi.trim() !== '',
            sehir: konum.sehir.trim() !== '',
            ilce: konum.ilce.trim() !== '',
            fullName: kontakKisi.fullName.trim() !== '',
            phone: phone.trim() !== '',
            email: iletisim.email.trim() !== '',
            validEmail: hasValidEmail,
            validPhone: hasValidPhone,
        });

        return hasRequiredFields && hasValidEmail && hasValidPhone;
    }, [tabelaAdi, konum.sehir, konum.ilce, kontakKisi.fullName, phone, iletisim.email]);

    React.useEffect(() => {
        if (isSaving || isDeleting) {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(refreshOverlayScale, {
                    toValue: 1,
                    speed: 10,
                    useNativeDriver: true,
                }),
            ]).start();

            // Start spinning animation
            Animated.loop(
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1000,
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        } else {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(refreshOverlayScale, {
                    toValue: 0.8,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [isSaving, isDeleting, spinValue, refreshOverlayOpacity, refreshOverlayScale]);


    return (
        <>
            <TouchableOpacity onPress={openModal} style={styles.container}>
                <Lucide name="square-plus" size={24} color="#6B4EFF" />
            </TouchableOpacity>

            {/* Modern Contact Form Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={handleCancel}
                statusBarTranslucent={true}
            >
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.modalOverlay}
                    enabled={Platform.OS === 'ios'}
                >
                    <TouchableWithoutFeedback onPress={handleCancel}>
                        <View style={styles.modalBackdrop} />
                    </TouchableWithoutFeedback>

                    <View style={styles.keyboardAvoidingView}>

                        <RefreshOverlay
                            refreshing={isSaving || isDeleting}
                            opacity={refreshOverlayOpacity}
                            scale={refreshOverlayScale}
                            spinValue={spinValue}
                        />

                        <Animated.View
                            style={[
                                styles.modalContainer,
                                {
                                    opacity: modalOpacityAnim,
                                    transform: [{ scale: modalScaleAnim }]
                                }
                            ]}
                        >
                            {/* Modal Header */}
                            <View style={styles.modalHeader}>
                                <View style={styles.modalHeaderContent}>
                                    <View style={styles.modalIconContainer}>
                                        <Lucide name="house-plug" size={24} color="#6B4EFF" />
                                    </View>
                                    <View style={styles.modalTitleContainer}>
                                        <Text style={styles.modalTitle}>{isEditing ? tabelaAdi + '' : 'Yeni Aday Bayi Ekle'}</Text>
                                        <Text style={styles.modalSubtitle}>{isEditing ? 'Bayi Bilgilerini düzenle' : 'Aday Bayi bilgilerini girin'}</Text>
                                    </View>
                                </View>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={handleCancel}
                                    disabled={isSaving}
                                >
                                    <Lucide name="x" size={20} color="#666" />
                                </TouchableOpacity>
                            </View>

                            {/* Modal Content with ScrollView */}
                            <ScrollView
                                style={styles.modalScrollView}
                                contentContainerStyle={styles.scrollContent}
                                showsVerticalScrollIndicator={false}
                                keyboardShouldPersistTaps="handled"
                            >
                                <View style={styles.modalContent}>
                                    {/* Full Name Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Tabela Adi *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="user" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={tabelaAdi}
                                                onChangeText={settabelaAdi}
                                                placeholder="Adayın tabela adı"
                                                autoCapitalize="words"
                                                autoCorrect={false}
                                                textContentType="name"
                                                keyboardType="default"
                                                returnKeyType="next"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>


                                    {/* Ticari Unvan */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Ticari Ünvan</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="briefcase" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={ticariUnvan}
                                                onChangeText={setTicariUnvan}
                                                placeholder="Şirketin resmi ünvanı"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                autoCapitalize="words"
                                                autoCorrect={false}
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                            />
                                        </View>
                                    </View>

                                    {/* Phone Input */}
                                    <View style={[styles.inputGroup, { marginTop: 0 }]}>
                                        <Text style={styles.inputLabel}>Telefon *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="phone" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={phone}
                                                onChangeText={setPhone}
                                                placeholder="Örn: +90 555 123 45 67"
                                                keyboardType="phone-pad"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                            />
                                        </View>
                                    </View>

                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>E-posta</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="mail" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={iletisim.email}
                                                autoCorrect={false}
                                                onChangeText={(text) => setIletisim({ ...iletisim, email: text })}
                                                placeholder="<EMAIL>"
                                                keyboardType="email-address"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                            />
                                        </View>
                                    </View>

                                    {/* Konum Bilgileri */}
                                    <View style={[styles.inputGroup, { marginTop: 0 }]}>
                                        <Text style={[styles.inputLabel, { fontSize: 14, color: '#6B4EFF' }]}>Konum Bilgileri</Text>

                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Adres</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="map" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    style={styles.input}
                                                    value={konum.adres}
                                                    autoCapitalize="words"
                                                    autoCorrect={false}
                                                    onChangeText={(text) => setKonum({ ...konum, adres: text })}
                                                    placeholder="Detaylı adres girin"
                                                    mode="outlined"
                                                    outlineColor="#E0E0E0"
                                                    activeOutlineColor="#6B4EFF"
                                                    disabled={isSaving}
                                                // multiline
                                                />
                                            </View>
                                        </View>

                                        <View style={styles.rowContainer}>
                                            <View style={[styles.inputGroup, { flex: 1 }]}>
                                                <Text style={styles.inputLabel}>Şehir</Text>
                                                <View style={styles.inputContainer}>
                                                    <Lucide name="map-pin" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                    <TextInput
                                                        style={styles.input}
                                                        value={konum.sehir}
                                                        autoCapitalize="words"
                                                        autoCorrect={false}
                                                        onChangeText={(text) => setKonum({ ...konum, sehir: text })}
                                                        placeholder="Şehir seçin"
                                                        mode="outlined"
                                                        outlineColor="#E0E0E0"
                                                        activeOutlineColor="#6B4EFF"
                                                        disabled={isSaving}
                                                    />
                                                </View>
                                            </View>

                                            <View style={[styles.inputGroup, { flex: 1, marginLeft: 12 }]}>
                                                <Text style={styles.inputLabel}>İlçe</Text>
                                                <View style={styles.inputContainer}>
                                                    <Lucide name="map-pin" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                    <TextInput
                                                        style={styles.input}
                                                        value={konum.ilce}
                                                        autoCapitalize="words"
                                                        autoCorrect={false}
                                                        onChangeText={(text) => setKonum({ ...konum, ilce: text })}
                                                        placeholder="İlçe girin"
                                                        mode="outlined"
                                                        outlineColor="#E0E0E0"
                                                        activeOutlineColor="#6B4EFF"
                                                        disabled={isSaving}
                                                    />
                                                </View>
                                            </View>
                                        </View>
                                    </View>

                                    {/* Kontak Kişi Bilgileri */}
                                    <View style={styles.contactCard}>
                                        <View style={styles.contactCardHeader}>
                                            <Lucide name="user" size={16} color="#6B4EFF" />
                                            <Text style={styles.contactCardTitle}>Kontak Kişi Bilgileri</Text>
                                        </View>

                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Tam Adı</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="user" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    style={styles.input}
                                                    value={kontakKisi.fullName}
                                                    autoCapitalize="words"
                                                    autoCorrect={false}
                                                    onChangeText={(text) => setKontakKisi({ ...kontakKisi, fullName: text })}
                                                    placeholder="Tam ad girin"
                                                    mode="outlined"
                                                    outlineColor="#E0E0E0"
                                                    activeOutlineColor="#6B4EFF"
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>

                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>E-posta</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="mail" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    style={styles.input}
                                                    value={kontakKisi.email}
                                                    onChangeText={(text) => setKontakKisi({ ...kontakKisi, email: text })}
                                                    placeholder="<EMAIL>" 
                                                    autoCorrect={false}
                                                    keyboardType="email-address"
                                                    mode="outlined"
                                                    outlineColor="#E0E0E0"
                                                    activeOutlineColor="#6B4EFF"
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>

                                        <View style={styles.inputGroup}>
                                            <Text style={styles.inputLabel}>Telefon</Text>
                                            <View style={styles.inputContainer}>
                                                <Lucide name="phone" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                                <TextInput
                                                    style={styles.input}
                                                    value={kontakKisi.phone}
                                                    onChangeText={(text) => setKontakKisi({ ...kontakKisi, phone: text })}
                                                    placeholder="+90 533 577 61 67"
                                                    keyboardType="phone-pad"
                                                    mode="outlined"
                                                    outlineColor="#E0E0E0"
                                                    activeOutlineColor="#6B4EFF"
                                                    disabled={isSaving}
                                                />
                                            </View>
                                        </View>
                                    </View>

                                    {/* Segment Seçimi */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Aday Bayi Tipi *</Text>
                                        <View style={styles.segmentContainer}>
                                            {segments.map((segment) => (
                                                <TouchableOpacity
                                                    key={segment}
                                                    style={[
                                                        styles.segmentChip,
                                                        selectedSegment === segment && styles.selectedSegmentChip
                                                    ]}
                                                    onPress={() => setSelectedSegment(segment)}
                                                    disabled={isSaving}
                                                >
                                                    <Text style={[
                                                        styles.segmentText, { fontSize: 10, color: '#333' },
                                                        selectedSegment === segment && styles.selectedSegmentText
                                                    ]}>
                                                        {segment.charAt(0).toUpperCase() + segment.slice(1)}
                                                    </Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    </View>
                                    {/* Kaynak Seçimi */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Aday Kaynağı</Text>
                                        <View style={styles.segmentContainer}>
                                            {sources.map((source) => (
                                                <TouchableOpacity
                                                    key={source}
                                                    style={[
                                                        styles.sourceChip,
                                                        selectedSources.includes(source) && styles.selectedSourceChip
                                                    ]}
                                                    onPress={() => {
                                                        setSelectedSources(prev =>
                                                            prev.includes(source)
                                                                ? prev.filter(s => s !== source)
                                                                : [...prev, source]
                                                        );
                                                    }}
                                                    disabled={isSaving}
                                                >
                                                    <Text style={[
                                                        styles.sourceText, { fontSize: 10, color: '#333' },
                                                        selectedSources.includes(source) && styles.selectedSourceText
                                                    ]}>
                                                        {source.charAt(0).toUpperCase() + source.slice(1)}
                                                    </Text>
                                                </TouchableOpacity>
                                            ))}
                                        </View>
                                    </View>

                                </View>
                            </ScrollView>

                            {/* Modal Actions */}
                            <View style={styles.modalActions}>
                                <View>
                                    <Text> </Text>
                                </View>
                                <View style={{ flexDirection: 'row', gap: 12 }}>
                                    <Button
                                        mode="outlined"
                                        onPress={handleCancel}
                                        style={[styles.actionButton, styles.cancelButton]}
                                        labelStyle={styles.cancelButtonText}
                                        disabled={isSaving}
                                    >
                                        İptal
                                    </Button>

                                    <Button
                                        mode="contained"
                                        onPress={handleSave}
                                        style={[
                                            styles.actionButton,
                                            (!isFormValid || isSaving) ? { backgroundColor: '#6B4EFF80' } : styles.saveButton
                                        ]}
                                        labelStyle={[styles.cancelButtonText, { color: 'white' }]}
                                        disabled={!isFormValid || isSaving}
                                        loading={isSaving}
                                    // icon={isSaving ? undefined : "check"}
                                    // icon={({ size, color }) => (
                                    //     <Lucide name="check" size={size} color={color || "#fff"} style={styles.checkboxIcon} />
                                    // )}
                                    >
                                        {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                    </Button>

                                </View>
                            </View>
                        </Animated.View>
                    </View>
                </KeyboardAvoidingView>
            </Modal>

        </>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 10,
    },

    modalOverlay: {
        flex: 1,
        backgroundColor: 'transparent',
    },
    modalBackdrop: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.25)',
    },
    keyboardAvoidingView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 20,
    },
    modalContainer: {
        width: '95%',
        maxWidth: 450,
        height: '80%',
        backgroundColor: 'white',
        borderRadius: 20,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.25,
        shadowRadius: 20,
        elevation: 12,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 24,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    modalHeaderContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    modalIconContainer: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#F0EDFF',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    modalTitleContainer: {
        flex: 1,
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: '700',
        color: '#1A1A1A',
        marginBottom: 4,
    },
    modalSubtitle: {
        fontSize: 12,
        color: '#666',
        fontWeight: '400',
    },
    closeButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        backgroundColor: '#F5F5F5',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalScrollView: {
        flex: 1,
        backgroundColor: 'white',
    },
    modalContent: {
        padding: 16,
        paddingTop: 16,
    },
    inputGroup: {
        marginBottom: 20,
    },
    segmentContainer: {
        flexDirection: 'row',
        gap: 8,
        marginTop: 4,
    },
    segmentChip: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#F5F5F5',
        borderWidth: 1,
        borderColor: '#E0E0E0',
    },
    selectedSegmentChip: {
        backgroundColor: '#6B4EFF',
        borderColor: '#6B4EFF',
    },
    segmentText: {
        fontSize: 14,
        color: '#666',
        fontWeight: '500',
    },
    selectedSegmentText: {
        color: 'white',
    },
    sourceChip: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        backgroundColor: '#F5F5F5',
        borderWidth: 1,
        borderColor: '#E0E0E0',
    },
    selectedSourceChip: {
        backgroundColor: '#FF6B6B',
        borderColor: '#FF6B6B',
    },
    sourceText: {
        fontSize: 14,
        color: '#666',
        fontWeight: '500',
    },
    selectedSourceText: {
        color: 'white',
    },
    contactCard: {
        backgroundColor: '#FAFBFF',
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#F0F0F0',
        padding: 16,
        marginTop: 20,
        marginBottom: 8,
    },
    contactCardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        paddingBottom: 12,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        marginBottom: 12,
    },
    contactCardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    rowContainer: {
        flexDirection: 'row',
        gap: 12,
    },
    inputLabel: {
        fontSize: 12,
        fontWeight: '600',
        color: '#1A1A1A',
        marginBottom: 2,
        marginLeft: 4,
    },
    inputContainer: {
        position: 'relative',
    },
    inputIcon: {
        position: 'absolute',
        left: 8,
        top: 14,
        zIndex: 1,
    },
    input: {
        paddingLeft: 14,
        paddingVertical: 5,
        fontSize: 14,
        backgroundColor: '#FAFAFA',
        paddingBottom: 5,
        padding: 0,
        height: 30,

        // borderWidth: 1,

    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        marginTop: 8,
        backgroundColor: '#F8F9FF',
        padding: 4,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#E8E5FF',
    },
    checkboxLabel: {
        fontSize: 15,
        color: '#333',
        marginLeft: 12,
        flex: 1,
        fontWeight: '500',
    },
    checkboxIcon: {
        marginLeft: 8,
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 16,
        gap: 12,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
        backgroundColor: '#FAFBFF',
    },
    actionButton: {
        minWidth: 40,
        borderRadius: 10,
        paddingVertical: 2,
    },
    cancelButton: {
        borderColor: '#E0E0E0',
        backgroundColor: 'transparent',
    },
    cancelButtonText: {
        color: '#666',
        fontSize: 14,
        fontWeight: '600',
        marginHorizontal: 8,
        marginVertical: 6,
    },
    saveButton: {
        backgroundColor: '#6B4EFF',
        elevation: 2,
        shadowColor: '#6B4EFF',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 8,
    },
    saveButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '700',
        marginHorizontal: 8,
        marginVertical: 6,
    },
    scrollContent: {
        flexGrow: 1,
    },
});

export default ProspectsAddNew;
