/* eslint-disable react-native/no-inline-styles */
import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Dimensions,
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { useNavigation, useRoute } from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import Header from '../../../navigation/Header';
import ImageViewerModal from './sections/ImageViewerModal.tsx';

// Import navigation types
import type { StackNavigationProp } from '@react-navigation/stack';
type ProfileStackParamList = {
  AllActivitiesPage: {
    activities: Activity[];
    dealerName: string;
  };
  ActivityDetailScreen: {
    activity: any;
    dealerName: string;
  };
};
type NavigationProp = StackNavigationProp<ProfileStackParamList>;

const { width } = Dimensions.get('window');

// Define the interface for activity data
interface Activity {
    id: string;
    activityType: string;
    activityNotes: string;
    createdAt: string;
    user: {
        name: string;
    };
    media?: Array<{
        s3FileUrl: string;
    }>;
}

const AllActivitiesPage = () => {
    const navigation = useNavigation<NavigationProp>();
    const route = useRoute();
    const { activities, dealerName } = route.params as { activities: Activity[]; dealerName: string };

    const [imageModalVisible, setImageModalVisible] = useState(false);
    const [selectedActivity, setSelectedActivity] = useState<any>(null);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);

    const openImageModal = (activity: any, index: number) => {
        setSelectedActivity(activity);
        setSelectedImageIndex(index);
        setImageModalVisible(true);
    };

    const closeImageModal = () => {
        setImageModalVisible(false);
    };

    return (
        <View style={styles.container}>
            <Header title={`${dealerName} - Tüm Aktiviteler`} showbackbutton />
            <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer}>
                {activities && activities.length > 0 ? (
                    <View style={styles.activityCardsContainer}>
                        {activities.map((activity: Activity) => (
                            <View key={activity.id} style={styles.activityCard}>
                                <TouchableOpacity 
                                    onPress={() => navigation.navigate('ActivityDetailScreen', { 
                                        activity: activity, 
                                        dealerName: dealerName
                                    })} style={styles.activityHeader}>
                                    {activity.activityType === 'ziyaret' && <Lucide name="map-pin" size={16} color="#6B4EFF" />}
                                    {activity.activityType === 'email' && <Lucide name="mail" size={16} color="#6B4EFF" />}
                                    {activity.activityType === 'telefon' && <Lucide name="phone" size={16} color="#6B4EFF" />}
                                    <Text style={styles.activityType}>
                                        {activity.activityType.charAt(0).toUpperCase() + activity.activityType.slice(1)}
                                    </Text>
                                    <Text style={styles.activityDate}>
                                        {activity?.user?.name} @ 
                                        {new Date(activity.createdAt).toLocaleDateString('tr-TR', {
                                            day: 'numeric',
                                            month: 'long',
                                            year: 'numeric'
                                        })}
                                    </Text>
                                </TouchableOpacity>
                                <TouchableOpacity 
                                    onPress={() => navigation.navigate('ActivityDetailScreen', { 
                                        activity: activity, 
                                        dealerName: dealerName
                                    })}
                                >
                                    <Text style={styles.activityNotes}>{activity.activityNotes}</Text>
                                </TouchableOpacity>
                                
                                {activity.media && activity.media.length > 0 && (
                                    <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaContainer}>
                                        {activity.media.map((media: any, index: number) => (
                                            <TouchableOpacity
                                                key={index}
                                                onPress={() => openImageModal(activity, index)}
                                            >
                                                <FastImage
                                                    style={styles.mediaThumbnail}
                                                    source={{
                                                        uri: media.s3FileUrl,
                                                        priority: FastImage.priority.normal,
                                                    }}
                                                    resizeMode={FastImage.resizeMode.contain}
                                                />
                                            </TouchableOpacity>
                                        ))}
                                    </ScrollView>
                                )}
                            </View>
                        ))}
                    </View>
                ) : (
                    <View style={styles.emptyContainer}>
                        <Text style={styles.emptyText}>Henüz aktivite eklenmemiş...</Text>
                    </View>
                )}
            </ScrollView>

            <ImageViewerModal
                imageModalVisible={imageModalVisible}
                selectedActivity={selectedActivity}
                selectedImageIndex={selectedImageIndex}
                closeImageModal={closeImageModal}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F5F5F5',
    },
    scrollContainer: {
        flex: 1,
    },
    contentContainer: {
        padding: 16,
        paddingBottom: 32,
    },
    activityCardsContainer: {
        gap: 12,
    },
    activityCard: {
        backgroundColor: '#FAFAFA',
        borderRadius: 12,
        padding: 16,
    },
    activityHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
    },
    activityType: {
        fontSize: 14,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    activityDate: {
        fontSize: 12,
        color: '#666',
        marginLeft: 'auto',
    },
    activityNotes: {
        fontSize: 12,
        color: '#666',
        marginTop: 8,
    },
    mediaContainer: {
        marginTop: 8,
    },
    mediaThumbnail: {
        width: 80,
        height: 80,
        borderRadius: 8,
        marginRight: 8,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 50,
    },
    emptyText: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
        fontSize: 16,
    },
});

export default AllActivitiesPage;
