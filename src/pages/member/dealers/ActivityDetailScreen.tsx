/* eslint-disable react-native/no-inline-styles */
import React, { useState } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Dimensions,
    Share,
    Alert,
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { useNavigation, useRoute } from '@react-navigation/native';
import FastImage from 'react-native-fast-image';
import Header from '../../../navigation/Header';
import ImageViewerModal from './sections/ImageViewerModal.tsx';

const { width } = Dimensions.get('window');

// Define the interface for activity data
interface Activity {
    id: string;
    activityType: string;
    activityNotes: string;
    createdAt: string;
    user: {
        name: string;
    };
    media?: Array<{
        s3FileUrl: string;
    }>;
}

const ActivityDetailScreen = () => {
    const navigation = useNavigation();
    const route = useRoute();
    const { activity, dealerName } = route.params as { activity: Activity; dealerName: string };

    const [imageModalVisible, setImageModalVisible] = useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);

    const openImageModal = (index: number) => {
        setSelectedImageIndex(index);
        setImageModalVisible(true);
    };

    const closeImageModal = () => {
        setImageModalVisible(false);
    };

    const handleShare = async () => {
        try {
            const message = `Aktivite: ${activity.activityType}\nNotlar: ${activity.activityNotes}\nTarih: ${new Date(activity.createdAt).toLocaleDateString('tr-TR')}`;
            await Share.share({
                message: message,
            });
        } catch (error) {
            Alert.alert('Hata', 'Paylaşım yapılamadı');
        }
    };

    const handleEdit = () => {
        // For now, we'll just show an alert
        // In a real implementation, this would navigate to an edit screen
        Alert.alert('Düzenle', 'Düzenleme özelliği yakında eklenecek');
    };

    const getActivityIcon = () => {
        switch (activity.activityType) {
            case 'ziyaret':
                return <Lucide name="map-pin" size={16} color="#6B4EFF" />;
            case 'email':
                return <Lucide name="mail" size={16} color="#6B4EFF" />;
            case 'telefon':
                return <Lucide name="phone" size={16} color="#6B4EFF" />;
            default:
                return <Lucide name="calendar" size={16} color="#6B4EFF" />;
        }
    };

    return (
        <View style={styles.container}>
            <Header
                title={`Aktivite Detayı`}
                showbackbutton
            // rightComponent={
            // }
            >
                <View style={styles.headerButtons}>
                    <TouchableOpacity style={styles.headerButton} onPress={handleShare}>
                        <Lucide name="share-2" size={20} color="#6B4EFF" />
                    </TouchableOpacity>
                    <TouchableOpacity style={styles.headerButton} onPress={handleEdit}>
                        <Lucide name="pencil" size={20} color="#6B4EFF" />
                    </TouchableOpacity>
                </View>
            </Header>
            <ScrollView style={styles.scrollContainer} contentContainerStyle={styles.contentContainer}>
                <View style={styles.activityCard}>
                    <View style={styles.activityHeader}>
                        {getActivityIcon()}
                        <Text style={styles.activityType}>
                            {activity.activityType.charAt(0).toUpperCase() + activity.activityType.slice(1)}
                        </Text>
                        <Text style={styles.activityDate}>
                            {activity?.user?.name} @ 
                            {new Date(activity.createdAt).toLocaleDateString('tr-TR', {
                                day: 'numeric',
                                month: 'long',
                                year: 'numeric'
                            })}
                        </Text>
                    </View>
                    <Text style={styles.activityNotes}>{activity.activityNotes}</Text>
                    
                    {activity.media && activity.media.length > 0 && (
                        <View style={styles.mediaContainer}>
                            <Text style={styles.mediaTitle}>Ekler</Text>
                            <View style={styles.mediaGrid}>
                                {activity.media.map((media: any, index: number) => (
                                    <TouchableOpacity
                                        key={index}
                                        onPress={() => openImageModal(index)}
                                        style={styles.mediaItem}
                                    >
                                        <FastImage
                                            style={styles.mediaThumbnail}
                                            source={{
                                                uri: media.s3FileUrl,
                                                priority: FastImage.priority.normal,
                                            }}
                                            resizeMode={FastImage.resizeMode.contain}
                                        />
                                    </TouchableOpacity>
                                ))}
                            </View>
                        </View>
                    )}
                    <View style={{height: 100}} />
                </View>
            </ScrollView>

            <ImageViewerModal
                imageModalVisible={imageModalVisible}
                selectedActivity={activity}
                selectedImageIndex={selectedImageIndex}
                closeImageModal={closeImageModal}
            />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F5F5F5',
    },
    scrollContainer: {
        flex: 1,
    },
    contentContainer: {
        padding: 16,
        paddingBottom: 32,
    },
    headerButtons: {
        flexDirection: 'row',
        gap: 16,
    },
    headerButton: {
        padding: 4,
    },
    activityCard: {
        backgroundColor: '#FAFAFA',
        borderRadius: 12,
        padding: 16,
    },
    activityHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 16,
    },
    activityType: {
        fontSize: 16,
        fontWeight: '600',
        color: '#6B4EFF',
    },
    activityDate: {
        fontSize: 12,
        color: '#666',
        marginLeft: 'auto',
    },
    activityNotes: {
        fontSize: 14,
        color: '#333',
        lineHeight: 20,
        marginBottom: 16,
    },
    mediaContainer: {
        marginTop: 16,
    },
    mediaTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
        marginBottom: 12,
    },
    mediaGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 12,
    },
    mediaItem: {
        marginBottom: 12,
        // borderWidth: 1,
    },
    mediaThumbnail: {
        width: (width - 90) / 2, // 3 items per row with padding
        height: (width - 90) / 2,
        borderRadius: 8,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 50,
    },
    emptyText: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
        fontSize: 16,
    },
});

export default ActivityDetailScreen;
