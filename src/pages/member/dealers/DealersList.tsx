/* eslint-disable react-native/no-inline-styles */
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Animated, Easing, TouchableOpacity, FlatList, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Lucide from '@react-native-vector-icons/lucide';
import { apiurls } from '../../../lib/contants.js';
import { useUser } from '../../../contexts/UserContext.tsx';
import { AsyncStorage, dbLocalTables, avgLifeTimeDays } from '../../../lib/fnx.storage.js';
import DealersListHeader from './List.Header.tsx';
import DummyHeader from './List.DummyHeader.tsx';
import RefreshOverlay from './List.RefreshOverlay.tsx';
//TODO: pagination.... handleListEnd...
//TODO: filter ... status... // sorting...



// Define the type for navigation parameters
type DealersDetailParams = {
  id: string;
  item: {
    id: string;
    tabelaAdi?: string;
    ticariUnvan?: string;
    segment: string;
    durum: string;
    konum: string;
  };
};

// Define the type for navigation
type DealersListNavigationProp = StackNavigationProp<{
  DealersDetail: DealersDetailParams;
}>;


const DealersList = () => {
  const { userInfo } = useUser();
  const navigation = useNavigation<DealersListNavigationProp>();
  const insets = useSafeAreaInsets();
  const [showStatusBar, setShowStatusBar] = useState(false);
  const [dealers, setDealers] = useState<{
    data: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
    }[]
  } | null>(null);

  const [dealers_base, setDealers_base] = useState<{
    data: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
    }[]
  } | null>(null);

  const [dealerSegmentSelected, setdealerSegmentSelected] = useState('');

  const [_loading, setLoading] = useState(true);
  const animatedValue = useRef(new Animated.Value(-insets.top)).current;
  const opacityValue = useRef(new Animated.Value(0)).current;
  const flatListRef = useRef(null);

  const handleScroll = (event: any) => {
    const scrollY = event.nativeEvent.contentOffset.y;
    const threshold = insets.top + 32; // 16 padding top + 16 padding bottom
    if (scrollY > threshold && !showStatusBar) {
      setShowStatusBar(true);
    } else if (scrollY <= threshold && showStatusBar) {
      setShowStatusBar(false);
    }
  };
  useEffect(() => {
    if (showStatusBar) {
      Animated.parallel([
        Animated.timing(animatedValue, {
          toValue: 0,
          duration: 300,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 1,
          duration: 300,
          easing: Easing.out(Easing.ease),
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(animatedValue, {
          toValue: -insets.top - 10,
          duration: 300,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        }),
        Animated.timing(opacityValue, {
          toValue: 0,
          duration: 300,
          easing: Easing.in(Easing.ease),
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [showStatusBar, animatedValue, opacityValue, insets.top]);

  const getdealers_live = useCallback(async () => {
    try {
      const uri = apiurls.getdealers;
      const res = await fetch(uri, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${userInfo?.token}`,
        },
      });
      setLoading(false);
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      console.log('live data', data)
      // setDealers(data);
      return data;
    } catch (error) {
      // console.error('Error:', error);
      setLoading(false);
      return false;
    }
  }, [userInfo?.token]);

  const getdealers = useCallback(async (force = false) => {
    if (!force) {
      let data;
      try {
        data = await AsyncStorage.getItemLimited(dbLocalTables.entity_dealers);
        setDealers(data);
        setDealers_base(data);
        setLoading(false);
      } catch (e) {
        if (e?.code && e?.code === 'nostoragedata') {
          console.log('e', e, dbLocalTables.entity_dealers)
          data = await getdealers_live();
          if (data) {
            await AsyncStorage.setItemLimitedwSec(
              dbLocalTables.entity_dealers,
              data,
              avgLifeTimeDays * 60 * 60 * 24,
            );
            setDealers(data);
            setDealers_base(data);
          } else {
            console.log('no data');
            setDealers({ data: [] })
            setDealers_base({ data: [] });
          }
        }
      }
    } else {
      let data = await getdealers_live();
      if (data) {
        await AsyncStorage.setItemLimitedwSec(
          dbLocalTables.entity_dealers,
          data,
          avgLifeTimeDays * 60 * 60 * 24,
        );
        setDealers(data);
        setDealers_base(data);
      } else {
        console.log('no data');
        setDealers({ data: [] });
        setDealers_base({ data: [] });
      }
    }
  }, [getdealers_live]);

  const [refreshing, setrefreshing] = useState(false);
  const refreshOverlayOpacity = useRef(new Animated.Value(0)).current;
  const refreshOverlayScale = useRef(new Animated.Value(0.8)).current;
  const spinValue = useRef(new Animated.Value(0)).current;

  const [searchQuery, setSearchQuery] = useState('');

  const act_search = () => {
    if (dealers && searchQuery && searchQuery !== '') {
      let txStg = [...dealers_base?.data] || [];
      const inputStr = searchQuery;
      const regexStr = inputStr.split(' ').join('|');
      const regex = new RegExp(regexStr, 'gi');
      txStg = [...txStg].filter(
        //({description}) => description.search(regex) >= 0,
        ({ tabelaAdi, ticariUnvan }) =>
          tabelaAdi.search(regex) >= 0 ||
          ticariUnvan.search(regex) >= 0,
      );

      if (dealerSegmentSelected && dealerSegmentSelected !== '') {
        txStg = [...txStg].filter(
          ({ segment }) => segment === dealerSegmentSelected,
        );
      }
      setDealers({ data: txStg });
    } else {
      let txStg = dealers_base && [...dealers_base?.data] || [];
      if (dealerSegmentSelected && dealerSegmentSelected !== '') {
        txStg = [...txStg].filter(
          ({ segment }) => segment === dealerSegmentSelected,
        );
      }
      setDealers({ data: txStg })
    }
  }
  useEffect(() => {
    // console.log('searchQuery updated...', searchQuery, dealers)
    act_search();
  }, [searchQuery]);

  const act_segment = () => {
    if (dealers && dealerSegmentSelected && dealerSegmentSelected !== '') {
      let txStg = [...dealers_base?.data] || []; 
       txStg = [...txStg].filter(
          ({ segment }) => segment === dealerSegmentSelected,
        );
      if (searchQuery && searchQuery !== '') {
        const inputStr = searchQuery;
        const regexStr = inputStr.split(' ').join('|');
        const regex = new RegExp(regexStr, 'gi');
        txStg = [...txStg].filter(
          //({description}) => description.search(regex) >= 0,
          ({ tabelaAdi, ticariUnvan }) =>
            tabelaAdi.search(regex) >= 0 ||
            ticariUnvan.search(regex) >= 0,
        );
      }

      setDealers({ data: txStg });
    } else {
      let txStg = dealers_base && [...dealers_base?.data] || [];
      if (searchQuery && searchQuery !== '') {
        // console.log('act segment... searchQuery... xzcz', searchQuery)
        const inputStr = searchQuery;
        const regexStr = inputStr.split(' ').join('|');
        const regex = new RegExp(regexStr, 'gi');
        txStg = [...txStg].filter(
          //({description}) => description.search(regex) >= 0,
          ({ tabelaAdi, ticariUnvan }) =>
            tabelaAdi.search(regex) >= 0 ||
            ticariUnvan.search(regex) >= 0,
        );
      }
      setDealers({data: txStg})
    }
  }
  useEffect(() => {
    // console.log('searchQuery updated...', dealerSegmentSelected)
    act_segment();
  }, [dealerSegmentSelected]);




  const refreshContent = async () => {
    setrefreshing(true);
    // console.log('refreshing...');
    await AsyncStorage.removeItemLimited(dbLocalTables.entity_dealers);
    await getdealers(true);
    setrefreshing(false);
  };

  const [dealers_vars, setdealers_vars] = useState(false);
  const getdealers_vars_live = useCallback(async () => {
    try {
      const uri = apiurls.getvars + 'bayiSegmentleri,bayilikDurumlari';
      console.log('uri vars', uri)
      const res = await fetch(uri, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${userInfo?.token}`,
        },
      });
      setLoading(false);
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      console.log('live vars', data)
      return data;
    } catch (error) {
      // console.error('Error:', error);
      setLoading(false);
      return false;
    }
  }, [userInfo?.token]);

  const getdealers_vars = useCallback(async (force = false) => {
    if (!force) {
      let data;
      try {
        data = await AsyncStorage.getItemLimited(dbLocalTables.entity_dealers_vars);
        setdealers_vars(data);
        setLoading(false);
      } catch (e) {
        if (e?.code && e?.code === 'nostoragedata') {
          console.log('e', e, dbLocalTables.entity_dealers_vars)
          data = await getdealers_vars_live();
          if (data) {
            await AsyncStorage.setItemLimitedwSec(
              dbLocalTables.entity_dealers_vars,
              data,
              avgLifeTimeDays * 60 * 60 * 24 * 100,
            );
            setdealers_vars(data);
          } else {
            console.log('no data1');
            setdealers_vars({ data: [] })
          }
        }
      }
    } else {
      let data = await getdealers_vars_live();
      if (data) {
        await AsyncStorage.setItemLimitedwSec(
          dbLocalTables.entity_dealers_vars,
          data,
          avgLifeTimeDays * 60 * 60 * 24 * 100,
        );
        setdealers_vars(data);
      } else {
        console.log('no data2');
        setdealers_vars({ data: [] });
      }
    }
  }, [getdealers_vars_live]);


  useEffect(() => {
    getdealers_vars();
    getdealers();
  }, [userInfo?.token, getdealers, getdealers_vars]);

  useEffect(() => {
    if (refreshing || _loading) {
      Animated.parallel([
        Animated.timing(refreshOverlayOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(refreshOverlayScale, {
          toValue: 1,
          speed: 10,
          useNativeDriver: true,
        }),
      ]).start();

      // Start spinning animation
      Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          easing: Easing.linear,
          useNativeDriver: true,
        })
      ).start();
    } else {
      Animated.parallel([
        Animated.timing(refreshOverlayOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(refreshOverlayScale, {
          toValue: 0.8,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [refreshing, refreshOverlayOpacity, refreshOverlayScale, spinValue, _loading]);

  // Render dealer item
  const renderDealerCard = ({ item }: {
    item: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
      acmFirstUser: any;
    }
  }) => {
    const displayName = item.tabelaAdi || item.ticariUnvan || 'İsimsiz Bayi';

    const getStatusColor = (status: string) => {
      switch (status) {
        case 'aktif':
          return '#4CAF50';
        case 'kapali':
          return '#F44336';
        default:
          return '#9E9E9E';
      }
    };

    const getSegmentColor = (segment: string) => {
      switch (segment) {
        case 'bayi':
          return '#2196F3';
        case 'sistem':
          return '#FF9800';
        default:
          return '#9E9E9E';
      }
    };

    return (
      <TouchableOpacity
        style={[styles.card, { borderLeftColor: getSegmentColor(item.segment), backgroundColor: item.durum === 'kapali' ? '#cecece' : '#fff' }]}
        onPress={() =>
          navigation.navigate('DealersDetail', { id: item.id, item: item })
        }
      >
        <View style={styles.cardRow}>
          <View style={styles.cardHeader}>
            <Text numberOfLines={2} style={styles.dealerName}>{displayName}</Text>
          </View>

          <View style={styles.cardContent}>
            <View style={[styles.statusBadge, { backgroundColor: getSegmentColor(item.segment) }]}>
              <Text style={styles.badgeText}>{item.segment}</Text>
            </View>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.durum) }]}>
              <Text style={styles.badgeText}>{item.durum}</Text>
            </View>
          </View>
        </View>

        <View style={[styles.locationRow, { justifyContent: 'space-between' }]}>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6, }}>
            <Lucide name="user" size={12} color="#666" />
            <Text style={[styles.locationText, { fontSize: 11 }]}>{item.acmFirstUser?.name}</Text>
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6, }}>
            <Lucide name="map-pin" size={12} color="#666" />
            <Text style={[styles.locationText, { fontSize: 11 }]}>{item.konum}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Render empty state
  const renderEmptyComponent = () => (
    <View style={styles.emptyContainer}>
      {!_loading && <Text style={styles.emptyText}>Bayi bulunamadı</Text>}
      {_loading && <Text style={styles.emptyText}>Yükleniyor..</Text>}
    </View>
  );

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.statusBar,
          {
            minHeight: insets.top + 0,
            transform: [{ translateY: animatedValue }],
            opacity: opacityValue
          }
        ]}
      >
        <DummyHeader navigation={navigation} />
      </Animated.View>
      <RefreshOverlay
        refreshing={refreshing}
        opacity={refreshOverlayOpacity}
        scale={refreshOverlayScale}
        spinValue={spinValue}
      />
      <FlatList
        ref={flatListRef}
        data={dealers?.data || []}
        renderItem={renderDealerCard}
        keyExtractor={(item: { id: string }) => item.id}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListHeaderComponent={
          <DealersListHeader
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            dealerSegmentSelected={dealerSegmentSelected}
            setDealerSegmentSelected={setdealerSegmentSelected}
            loading={_loading}
            dealers_vars={dealers_vars}
            dealerCount={dealers?.data?.length || 0}
          />
        }
        ListEmptyComponent={renderEmptyComponent}
        ListFooterComponent={ListFooterComponent}
        onScroll={handleScroll}
        scrollEventThrottle={16}
        refreshControl={
          <RefreshControl
            tintColor="#000"
            refreshing={refreshing}
            onRefresh={refreshContent}
          />
        }

      />
      {/* <Text>Test alanı..{searchQuery}</Text> */}
    </View>
  );
};

const ListFooterComponent = () => {
  return (
    <View style={{ height: 90 }}>
      <Text> </Text>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: '#f5f5f5',
    // paddingBottom: 80,
  },
  statusBar: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    zIndex: 1000,
  },
  listContent: {
    paddingHorizontal: 0,
    paddingBottom: 20,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderLeftWidth: 4,
    borderLeftColor: '#6B4EFF',
    marginHorizontal: 16,
  },
  cardHeader: {
    marginBottom: 12,
    flex: 1,
    marginRight: 12,
  },
  dealerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1A1A1A',
    flexShrink: 1,
  },
  cardContent: {
    gap: 4,
    flexWrap: 'wrap',
    flexDirection: 'row',
    minWidth: 90,
    flexShrink: 0,
  },
  cardRow: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
    textTransform: 'capitalize',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationText: {
    fontSize: 14,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});

export default DealersList;
