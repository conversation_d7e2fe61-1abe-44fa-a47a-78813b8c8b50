import React from 'react';
import { Animated, View, Text, StyleSheet } from 'react-native';

interface RefreshOverlayProps {
  refreshing: boolean;
  opacity: Animated.Value;
  scale: Animated.Value;
  spinValue: Animated.Value;
  overlayText?: string;
}

const RefreshOverlay = ({ refreshing, opacity, scale, spinValue, overlayText }: RefreshOverlayProps) => {
  if (!refreshing) return null;

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg']
  });

  return (
    <Animated.View style={[styles.refreshOverlay, { opacity }]}>
      <Animated.View style={{ transform: [{ scale }] }}>
        <View style={styles.refreshContainer}>
          <Animated.View style={[styles.refreshSpinner, { transform: [{ rotate: spin }] }]} />
          <Text style={styles.refreshText}>{overlayText || 'Yenileniyor...'}</Text>
        </View>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  refreshOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  refreshContainer: {
    backgroundColor: 'white',
    borderRadius: 16,
    padding: 24,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  refreshSpinner: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderColor: '#6B4EFF',
    borderTopColor: 'transparent',
    marginBottom: 16,
  },
  refreshText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
});

export default RefreshOverlay;
