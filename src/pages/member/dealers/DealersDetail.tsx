/* eslint-disable react-native/no-inline-styles */
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { View, Text, StyleSheet, Easing, Animated, ScrollView, ActivityIndicator, TouchableOpacity, ToastAndroid, Platform, Alert, RefreshControl } from 'react-native';
import Clipboard from '@react-native-clipboard/clipboard';
import Lucide from '@react-native-vector-icons/lucide';
import { apiurls } from '../../../lib/contants.js';
import { useUser } from '../../../contexts/UserContext.tsx';
import { AsyncStorage, dbLocalTables, avgLifeTimeDays } from '../../../lib/fnx.storage.js';
import Header from '../../../navigation/Header';
import RefreshOverlay from './List.RefreshOverlay.tsx';
import RenderContactPeople from './sections/RenderContactPeople.tsx';
import RenderDokumanlar from './sections/RenderDokumanlar';
import RenderAktiviteler from './sections/RenderAktiviteler';
import RenderTasks from './sections/RenderTasks';
import RenderIndicators from './sections/RenderIndicators';
import RenderContactInfo from './sections/RenderContactInfo';
import RenderLocationInfo from './sections/RenderLocationInfo';
import RenderOtherInfo from './sections/RenderOtherInfo';

//TODO: tags in acente bilgileri...

// Define the interface for route params
interface DealersDetailRouteParams {
    id: string | number;
    item: any;
}

// Define the interface for the component props
interface DealersDetailProps {
    route: {
        params: DealersDetailRouteParams;
    };
}

// Define the interface for dealer data
interface DealerData {
    tip: string;
    segment: string;
    tabelaAdi: string;
    ticariUnvan: string;
    durum: string;
    konum: {
        sehir: string;
        ilce: string;
        adres: string;
    };
    diger: {
        vd: string;
        vdNo: string;
        tursabNo: string;
        bankaIban: string;
        banka: string;
    };
    iletisim: {
        instagram: string;
        webSitesi: string;
        cep: string;
        ofis: string;
        email: string;
    };
    backoffice: {
        agent_bo_code: string;
        account_code: string;
    };
    created_at: string;
    updated_at: string;
    id: string;
}

const DealersDetail: React.FC<DealersDetailProps> = (props) => {
    const { id, item } = props.route.params;
    let storageName = dbLocalTables.entity_dealer + ':' + id;
    const { userInfo } = useUser();
    const [dealer, setDealer] = useState<DealerData | null>(item || null);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState('iletisim'); // New state for active tab
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;

    const [refreshing, setrefreshing] = useState(false);
    const refreshOverlayOpacity = useRef(new Animated.Value(0)).current;
    const refreshOverlayScale = useRef(new Animated.Value(0.8)).current;
    const spinValue = useRef(new Animated.Value(0)).current;

    const [updateMe, setUpdateMe] = useState(0);
    

    const refreshContent = async () => {
        setrefreshing(true);
        setUpdateMe(Date.now());
        console.log('refreshing...');
        await AsyncStorage.removeItemContainsLimited(storageName);
        await getdealer(true);
        setrefreshing(false);
    };


    useEffect(() => {
        if (refreshing || loading) {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(refreshOverlayScale, {
                    toValue: 1,
                    speed: 10,
                    useNativeDriver: true,
                }),
            ]).start();

            // Start spinning animation
            Animated.loop(
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1000,
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        } else {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(refreshOverlayScale, {
                    toValue: 0.8,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [refreshing, refreshOverlayOpacity, refreshOverlayScale, spinValue, loading]);


    const getdealer_live = useCallback(async () => {
        try {
            const uri = apiurls.getdealer + id;
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
            });
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            console.log('live dealer data', data)
            return data;
        } catch (error) {
            console.log('fetch error', error);
            return false;
        }
    }, [userInfo?.token, id]);

    const getdealer = useCallback(async (force = false) => {
        if (!force) {
            let data;
            try {
                data = await AsyncStorage.getItemLimited(storageName);
                console.log('AsyncStorage data?.data', storageName, data?.data)
                if (Array.isArray(data?.data)) {
                    data = data?.data[0];
                    if (!data) {
                        data = await getdealer_live();
                        await AsyncStorage.setItemLimitedwSec(
                            storageName,
                            data,
                            avgLifeTimeDays * 60 * 60 * 24,
                        );
                        if (data) {
                            await AsyncStorage.setItemLimitedwSec(
                                storageName,
                                data,
                                avgLifeTimeDays * 60 * 60 * 24,
                            );
                            setDealer(data?.data);
                            setLoading(false);
                        } else {
                            setDealer(null);
                            setLoading(false);
                        }
                    } else {
                        setDealer(data);
                        setLoading(false);
                    }
                    // !data && getdealer_live();
                    // console.log('data', data)
                } else {
                    setDealer(data?.data);
                    setLoading(false);
                }
                // setDealer(data?.data);
            } catch (e) {
                if ((e as any)?.code && (e as any)?.code === 'nostoragedata') {
                    data = await getdealer_live();
                    if (data) {
                        await AsyncStorage.setItemLimitedwSec(
                            storageName,
                            data,
                            avgLifeTimeDays * 60 * 60 * 24,
                        );
                        setDealer(data?.data);
                    } else {
                        setDealer(null);
                    }
                }
            }
        } else {
            let data = await getdealer_live();
            if (data) {
                await AsyncStorage.setItemLimitedwSec(
                    storageName,
                    data,
                    avgLifeTimeDays * 60 * 60 * 24,
                );
                setDealer(data?.data);
            } else {
                setDealer(null);
            }
        }
        setLoading(false);
    }, [getdealer_live, storageName]);

    useEffect(() => {
        getdealer();
    }, [userInfo?.token, getdealer]);

    useEffect(() => {
        if (!loading && dealer) {
            Animated.parallel([
                Animated.timing(fadeAnim, {
                    toValue: 1,
                    duration: 500,
                    useNativeDriver: true,
                }),
                Animated.timing(slideAnim, {
                    toValue: 0,
                    duration: 500,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [loading, dealer, fadeAnim, slideAnim]);

    // Function to copy text to clipboard
    const copyToClipboard = (text: string, label: string) => {
        Clipboard.setString(text);
        if (Platform.OS === 'android') {
            ToastAndroid.show(`${label} kopyalandı`, ToastAndroid.SHORT);
        } else {
            Alert.alert(`${label} kopyalandı`);
        }
    };

    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);
    // const contactAnimations = React.useRef(new Animated.Value(1)).current;

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
    };

    const renderBusinessInfo = () => {
        return (
            <Animated.View style={[styles.card, { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }]}>
                <View style={styles.cardHeader}>
                    <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', gap: 2, }} onPress={() => toggleContactCollapse()} >
                        <Lucide name="briefcase" size={20} color="#6B4EFF" />
                        <Text style={styles.cardTitle}>Acente Bilgileri</Text>
                        <Lucide
                            name={!isCollapsed ? "chevron-down" : "chevron-up"}
                            size={20}
                            color="#666"
                        />
                    </TouchableOpacity>
                    <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginLeft: 'auto' }}>
                        {dealer?.segment && (<Text style={styles.infoValue_s}>{dealer.segment}</Text>)}
                        {dealer?.durum && (<Text style={[styles.infoValue_s, dealer.durum === 'aktif' ? styles.activeStatus : styles.inactiveStatus]}>
                            {dealer.durum}
                        </Text>)}
                    </View> 
                </View>
                {isCollapsed && (<View>
                    <View style={[styles.cardContent, { paddingBottom: 0 }]}>
                        {dealer?.tabelaAdi && (
                            <View style={styles.infoRow}>
                                <Lucide name="signpost" size={16} color="#666" />
                                {/* <Text style={styles.infoLabel}>Tabela Adı:</Text> */}
                                <TouchableOpacity onLongPress={() => copyToClipboard(dealer.tabelaAdi, 'Tabela adı')}>
                                    <Text style={styles.infoValue_t}>{dealer.tabelaAdi}</Text>
                                </TouchableOpacity>
                            </View>
                        )}
                        {dealer?.ticariUnvan && (
                            <View style={[styles.infoRow, { paddingLeft: 16 }]}>
                                {/* <Lucide name="file-text" size={16} color="#666" /> */}
                                {/* <Text style={styles.infoLabel}>Ticari Ünvan:</Text> */}
                                <TouchableOpacity onLongPress={() => copyToClipboard(dealer.ticariUnvan, 'Ticari ünvan')}>
                                    <Text style={styles.infoValue_t}>{dealer.ticariUnvan}</Text>
                                </TouchableOpacity>
                            </View>
                        )}
                    </View>
                    {/* Tab Headers */}
                    <View style={[styles.tabContainer, { borderTopWidth: 0.5, marginTop: 8, borderTopColor: '#eee' }]}>
                        <TouchableOpacity
                            style={[styles.tab, activeTab === 'iletisim' && styles.activeTab]}
                            onPress={() => setActiveTab('iletisim')}
                        >
                            <Text style={[styles.tabText, activeTab === 'iletisim' && styles.activeTabText]}>İletişim</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.tab, activeTab === 'konum' && styles.activeTab]}
                            onPress={() => setActiveTab('konum')}
                        >
                            <Text style={[styles.tabText, activeTab === 'konum' && styles.activeTabText]}>Konum</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.tab, activeTab === 'diger' && styles.activeTab]}
                            onPress={() => setActiveTab('diger')}
                        >
                            <Text style={[styles.tabText, activeTab === 'diger' && styles.activeTabText]}>Diger</Text>
                        </TouchableOpacity>
                    </View>

                    {/* Tab Content */}
                    <View style={styles.cardContent}>
                        {/* Tabbed Content */}
                        <View style={styles.tabContentContainer}>
                            {activeTab === 'iletisim' && <RenderContactInfo iletisim={dealer?.iletisim} copyToClipboard={copyToClipboard} />}
                            {activeTab === 'konum' && <RenderLocationInfo konum={dealer?.konum} copyToClipboard={copyToClipboard} />}
                            {activeTab === 'diger' && <RenderOtherInfo diger={dealer?.diger} backoffice={dealer?.backoffice} timestamps={{ createdAt: dealer?.created_at, updatedAt: dealer?.updated_at }} copyToClipboard={copyToClipboard} />}
                        </View>
                    </View>
                </View>
                )}

            </Animated.View>
        );
    };

    if (loading) {
        return (
            <View style={styles.container}>
                <Header title={item.tabelaAdi || "Acente Detayı"} showbackbutton />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#6B4EFF" />
                    <Text style={styles.loadingText}>Acente bilgileri yükleniyor...</Text>
                </View>
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <Header title={dealer?.tabelaAdi || item.tabelaAdi || "Acente Detayı"} showbackbutton>
                {/* <TouchableOpacity onPress={async () => await AsyncStorage.removeItemLimited(storageName)}>
                    <Text style={styles.clearCacheText}>R</Text>
                </TouchableOpacity> */}
            </Header>

            <RefreshOverlay
                refreshing={refreshing}
                opacity={refreshOverlayOpacity}
                scale={refreshOverlayScale}
                spinValue={spinValue}
            />
            <ScrollView
                style={styles.scrollContainer}
                contentContainerStyle={styles.contentContainer}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        tintColor="#000"
                        refreshing={refreshing}
                        onRefresh={refreshContent}
                    />
                }
            >
                {dealer ? (
                    <>
                        <RenderIndicators
                            createdAt={dealer?.created_at}
                            updatedAt={dealer?.updated_at}
                            copyToClipboard={copyToClipboard}
                            fadeAnim={fadeAnim}
                            slideAnim={slideAnim}
                        />
                        {renderBusinessInfo()}
                        <RenderContactPeople
                            dealer={dealer}
                            refreshContent={refreshContent}
                            copyToClipboard={copyToClipboard}
                            fadeAnim={fadeAnim}
                            slideAnim={slideAnim}
                        />
                        <RenderAktiviteler
                            dealer={dealer}
                            refreshContent={refreshContent}
                            copyToClipboard={copyToClipboard}
                            fadeAnim={fadeAnim}
                            slideAnim={slideAnim}
                            updateMe={updateMe}
                        />
                        <RenderTasks
                            fadeAnim={fadeAnim}
                            slideAnim={slideAnim}
                        />
                        <RenderDokumanlar
                            fadeAnim={fadeAnim}
                            slideAnim={slideAnim}
                        />
                        <View style={{ height: 60 }}>
                            <Text>&nbsp;</Text>
                        </View>
                    </>
                ) : (
                    <View style={styles.errorContainer}>
                        <Text style={styles.errorText}>Acente bilgileri bulunamadı</Text>
                        <Text style={styles.errorSubtext}>Lütfen daha sonra tekrar deneyiniz</Text>
                    </View>
                )}
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F5F5F5',
    },
    scrollContainer: {
        flex: 1,
    },
    contentContainer: {
        padding: 16,
        paddingBottom: 32,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#666',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        padding: 32,
    },
    errorText: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
        marginTop: 16,
        textAlign: 'center',
    },
    errorSubtext: {
        fontSize: 14,
        color: '#666',
        marginTop: 8,
        textAlign: 'center',
    },
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
    infoValue_t: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'left',
        marginLeft: 5,
    },
    infoValue_s: {
        fontSize: 12,
        color: '#333',
        fontWeight: '500',
        textAlign: 'right',
    },
    activeStatus: {
        color: '#4CAF50',
        fontWeight: '600',
    },
    inactiveStatus: {
        color: '#FF6B6B',
        fontWeight: '600',
    },
    // Tab styles
    tabContainer: {
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
        marginHorizontal: 16,
    },
    tab: {
        flex: 1,
        paddingVertical: 8,
        alignItems: 'center',
        borderBottomWidth: 2,
        borderBottomColor: 'transparent',
    },
    activeTab: {
        borderBottomColor: '#6B4EFF',
    },
    tabText: {
        fontSize: 11,
        color: '#666',
        fontWeight: '500',
    },
    activeTabText: {
        color: '#6B4EFF',
        fontWeight: '600',
    },
    tabContentContainer: {
        marginTop: 8,
    },
    tabContent: {
        paddingVertical: 8,
    },
});

export default DealersDetail;