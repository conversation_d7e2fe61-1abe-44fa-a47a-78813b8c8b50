import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    ScrollView,
    Modal,
    KeyboardAvoidingView,
    TouchableWithoutFeedback,
    Platform,
    Image,
    Animated
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { TextInput, Button } from 'react-native-paper';
import RefreshOverlay from '../List.RefreshOverlay.tsx';
import { stylex } from '../../../../lib/styles.dealerAktivite.js';

interface ActivityFormModalProps {
    modalVisible: boolean;
    isSaving: boolean;
    isDeleting: boolean;
    activityType: string;
    activityNotes: string;
    selectedImages: any[];
    userLocation: { latitude: number; longitude: number } | null;
    isGettingLocation: boolean;
    modalOpacityAnim: Animated.Value;
    modalScaleAnim: Animated.Value;
    refreshOverlayOpacity: Animated.Value;
    refreshOverlayScale: Animated.Value;
    spinValue: Animated.Value;
    setActivityType: (type: string) => void;
    setActivityNotes: (notes: string) => void;
    handleTakePhoto: () => void;
    handleSelectFromGallery: () => void;
    handleRemoveImage: (index: number) => void;
    getUserLocation: () => void;
    handleSave: () => void;
    handleCancel: () => void;
}

const ActivityFormModal: React.FC<ActivityFormModalProps> = (props) => {
    const {
        modalVisible,
        isSaving,
        isDeleting,
        activityType,
        activityNotes,
        selectedImages,
        userLocation,
        isGettingLocation,
        modalOpacityAnim,
        modalScaleAnim,
        refreshOverlayOpacity,
        refreshOverlayScale,
        spinValue,
        setActivityType,
        setActivityNotes,
        handleTakePhoto,
        handleSelectFromGallery,
        handleRemoveImage,
        getUserLocation,
        handleSave,
        handleCancel,
        closeModal
    } = props;

    return (
        <Modal
            visible={modalVisible}
            transparent={true}
            animationType="slide"
            onRequestClose={handleCancel}
            statusBarTranslucent={true}
        >
            <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                style={styles.modalOverlay}
                enabled={Platform.OS === 'ios'}
            >
                <TouchableWithoutFeedback onPress={handleCancel}>
                    <View style={styles.modalBackdrop} />
                </TouchableWithoutFeedback>

                <View style={styles.keyboardAvoidingView}>

                    <RefreshOverlay
                        refreshing={isSaving || isDeleting}
                        opacity={refreshOverlayOpacity}
                        scale={refreshOverlayScale}
                        spinValue={spinValue}
                        overlayText="Kaydediliyor..."
                    />

                    <Animated.View
                        style={[
                            styles.modalContainer,
                            {
                                opacity: modalOpacityAnim,
                                transform: [{ scale: modalScaleAnim }]
                            }
                        ]}
                    >
                        {/* Modal Header */}
                        <View style={styles.modalHeader}>
                            <View style={styles.modalHeaderContent}>
                                <View style={styles.modalIconContainer}>
                                    <Lucide name="calendar-plus" size={24} color="#6B4EFF" />
                                </View>
                                <View style={styles.modalTitleContainer}>
                                    <Text style={styles.modalTitle}>{'Yeni Aktivite Ekle'}</Text>
                                    <Text style={styles.modalSubtitle}>{'Bayi ziyareti, telefon görüşmesi veya email kaydı oluşturun'}</Text>
                                </View>
                            </View>
                            <TouchableOpacity
                                style={styles.closeButton}
                                onPress={handleCancel}
                                disabled={isSaving}
                            >
                                <Lucide name="x" size={20} color="#666" />
                            </TouchableOpacity>
                        </View>

                        {/* Modal Content with ScrollView */}
                        <ScrollView
                            style={styles.modalScrollView}
                            showsVerticalScrollIndicator={false}
                            keyboardShouldPersistTaps="handled"
                        >
                            <View style={styles.modalContent}>
                                {/* Activity Type Selector */}
                                <View style={styles.inputGroup}>
                                    <Text style={styles.inputLabel}>Aktivite Türü *</Text>
                                    <View style={styles.typeSelector}>
                                        <TouchableOpacity
                                            style={[
                                                styles.typeOption,
                                                activityType === 'ziyaret' && styles.typeOptionActive
                                            ]}
                                            onPress={() => setActivityType('ziyaret')}
                                            disabled={isSaving}
                                        >
                                            <Lucide
                                                name="map-pin"
                                                size={20}
                                                color={activityType === 'ziyaret' ? '#6B4EFF' : '#666'}
                                            />
                                            <Text style={[
                                                styles.typeOptionText,
                                                activityType === 'ziyaret' && styles.typeOptionTextActive
                                            ]}>
                                                Ziyaret
                                            </Text>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                styles.typeOption,
                                                activityType === 'telefon' && styles.typeOptionActive
                                            ]}
                                            onPress={() => setActivityType('telefon')}
                                            disabled={isSaving}
                                        >
                                            <Lucide
                                                name="phone"
                                                size={20}
                                                color={activityType === 'telefon' ? '#6B4EFF' : '#666'}
                                            />
                                            <Text style={[
                                                styles.typeOptionText,
                                                activityType === 'telefon' && styles.typeOptionTextActive
                                            ]}>
                                                Telefon
                                            </Text>
                                        </TouchableOpacity>

                                        <TouchableOpacity
                                            style={[
                                                styles.typeOption,
                                                activityType === 'email' && styles.typeOptionActive
                                            ]}
                                            onPress={() => setActivityType('email')}
                                            disabled={isSaving}
                                        >
                                            <Lucide
                                                name="mail"
                                                size={20}
                                                color={activityType === 'email' ? '#6B4EFF' : '#666'}
                                            />
                                            <Text style={[
                                                styles.typeOptionText,
                                                activityType === 'email' && styles.typeOptionTextActive
                                            ]}>
                                                Email
                                            </Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>

                                {/* Activity Notes */}
                                <View style={styles.inputGroup}>
                                    <Text style={styles.inputLabel}>Aktivite Notları *</Text>
                                    <View style={[styles.textAreaContainer, { borderRadius: 30 }]}>
                                        <TextInput
                                            style={[styles.textArea]}
                                            value={activityNotes}
                                            onChangeText={setActivityNotes}
                                            placeholder="Aktivite detaylarını buraya yazın..."
                                            multiline
                                            numberOfLines={6}
                                            mode="outlined"
                                            outlineColor="#E0E0E0"
                                            activeOutlineColor="#6B4EFF"
                                            autoCorrect={false}
                                            autoCapitalize='none'
                                            disabled={isSaving}
                                            theme={{
                                                colors: {
                                                    background: '#FAFAFA',
                                                    onSurfaceVariant: '#999',
                                                },
                                                roundness: 30
                                            }}
                                        />
                                    </View>
                                </View>

                                {/* Location Info */}
                                <View style={styles.locationInfo}>
                                    <View style={styles.locationHeader}>
                                        <Lucide name="map-pin" size={16} color="#6B4EFF" />
                                        <Text style={styles.locationTitle}>Konum Bilgisi</Text>
                                    </View>
                                    {isGettingLocation ? (
                                        <View style={styles.locationLoading}>
                                            <Text style={styles.locationLoadingText}>Konum alınıyor...</Text>
                                        </View>
                                    ) : userLocation ? (
                                        <View style={styles.locationDetails}>
                                            <Text style={styles.locationText}>
                                                📍 Lat: {userLocation.latitude.toFixed(6)}, Lng: {userLocation.longitude.toFixed(6)}
                                            </Text>
                                        </View>
                                    ) : (
                                        <View style={styles.locationError}>
                                            <Text style={styles.locationErrorText}>Konum alınamadı</Text>
                                            <TouchableOpacity onPress={getUserLocation} style={styles.retryButton}>
                                                <Text style={styles.retryButtonText}>Tekrar Dene</Text>
                                            </TouchableOpacity>
                                        </View>
                                    )}
                                </View>

                                {/* Photo Upload Section - Only for Ziyaret */}
                                {activityType === 'ziyaret' && (
                                    <View style={[styles.inputGroup, { marginTop: 20 }]}>
                                        <Text style={styles.inputLabel}>Fotoğraflar</Text>

                                        {/* Photo Action Buttons */}
                                        <View style={styles.photoActions}>
                                            <TouchableOpacity
                                                style={styles.photoActionButton}
                                                onPress={handleTakePhoto}
                                                disabled={isSaving}
                                            >
                                                <View style={styles.photoActionIcon}>
                                                    <Lucide name="camera" size={24} color="#6B4EFF" />
                                                </View>
                                                <Text style={styles.photoActionText}>Fotoğraf Çek</Text>
                                            </TouchableOpacity>

                                            <TouchableOpacity
                                                style={styles.photoActionButton}
                                                onPress={handleSelectFromGallery}
                                                disabled={isSaving}
                                            >
                                                <View style={styles.photoActionIcon}>
                                                    <Lucide name="image" size={24} color="#6B4EFF" />
                                                </View>
                                                <Text style={styles.photoActionText}>Galeriden Seç</Text>
                                            </TouchableOpacity>
                                        </View>

                                        {/* Selected Images Grid */}
                                        {selectedImages.length > 0 && (
                                            <View style={styles.imagesGrid}>
                                                {selectedImages.map((image, index) => (
                                                    <View key={index} style={styles.imageContainer}>
                                                        <Image
                                                            source={{ uri: image.uri }}
                                                            style={styles.imageThumb as any}
                                                            resizeMode="cover"
                                                        />
                                                        <TouchableOpacity
                                                            style={styles.removeImageButton}
                                                            onPress={() => handleRemoveImage(index)}
                                                            disabled={isSaving}
                                                        >
                                                            <Lucide name="x" size={16} color="#fff" />
                                                        </TouchableOpacity>
                                                    </View>
                                                ))}
                                            </View>
                                        )}
                                    </View>
                                )}

                            </View>
                        </ScrollView>

                        {/* Modal Actions */}
                        <View style={styles.modalActions}>
                            <View>
                                <Text> </Text>
                            </View>
                            <View style={{ flexDirection: 'row', gap: 12 }}>
                                <Button
                                    mode="outlined"
                                    onPress={handleCancel}
                                    style={[styles.actionButton, styles.cancelButton]}
                                    labelStyle={styles.cancelButtonText}
                                    disabled={isSaving}
                                >
                                    İptal
                                </Button>
                                <Button
                                    mode="contained"
                                    onPress={handleSave}
                                    style={[styles.actionButton, (isSaving ? { backgroundColor: '#6B4EFF80' } : styles.saveButton)]}
                                    labelStyle={[styles.cancelButtonText, { color: 'white' }]}
                                    disabled={isSaving}
                                    loading={isSaving}
                                >
                                    {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                </Button>
                            </View>
                        </View>
                    </Animated.View>
                </View>
            </KeyboardAvoidingView>
        </Modal>
    );
};

const styles = StyleSheet.create({
    ...stylex as any,
});

export default ActivityFormModal;
