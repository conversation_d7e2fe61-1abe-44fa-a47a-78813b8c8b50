import React from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Modal,
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import FastImage from 'react-native-fast-image';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Reanimated, {
    useSharedValue,
    useAnimatedStyle,
    withSpring
} from 'react-native-reanimated';
import { stylex } from '../../../../lib/styles.dealerAktivite.js';

interface ImageViewerModalProps {
    imageModalVisible: boolean;
    selectedActivity: any;
    selectedImageIndex: number;
    closeImageModal: () => void;
}

const ImageViewerModal: React.FC<ImageViewerModalProps> = (props) => {
    const {
        imageModalVisible,
        selectedActivity,
        selectedImageIndex,
        closeImageModal
    } = props;

    // Gesture handlers for image zoom/pan
    const scale = useSharedValue(1);
    const translateX = useSharedValue(0);
    const translateY = useSharedValue(0);

    const resetTransform = React.useCallback(() => {
        scale.value = withSpring(1);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
    }, [scale, translateX, translateY]);

    React.useEffect(() => {
        if (imageModalVisible) {
            resetTransform();
        }
    }, [imageModalVisible, resetTransform]);

    const gesture = Gesture.Simultaneous(
        Gesture.Pinch()
            .onUpdate((e: any) => {
                scale.value = e.scale;
            }),
        Gesture.Pan()
            .onUpdate((e: any) => {
                translateX.value = e.translationX;
                translateY.value = e.translationY;
            })
    );

    // Animated style for the image
    const imageStyle = useAnimatedStyle(() => {
        return {
            transform: [
                { scale: scale.value },
                { translateX: translateX.value },
                { translateY: translateY.value },
            ],
        };
    });

    return (
        <Modal
            visible={imageModalVisible}
            transparent={true}
            animationType="fade"
            onRequestClose={closeImageModal}
        >
            <View style={styles.imageModalContainer}>
                <TouchableOpacity
                    style={styles.closeButtonImageModal}
                    onPress={closeImageModal}
                >
                    <Lucide name="x" size={24} color="#fff" />
                </TouchableOpacity>

                {selectedActivity?.media?.[selectedImageIndex] && (
                    <GestureDetector gesture={gesture}>
                        <Reanimated.View style={[styles.imageModalContent, imageStyle]}>
                            <FastImage
                                source={{ uri: selectedActivity.media[selectedImageIndex].s3FileUrl }}
                                style={{ width: '100%', height: '100%' }}
                                resizeMode="contain"
                            />
                        </Reanimated.View>
                    </GestureDetector>
                )}
            </View>
        </Modal>
    );
};

const styles = StyleSheet.create({
    ...stylex as any,
});

export default ImageViewerModal;
