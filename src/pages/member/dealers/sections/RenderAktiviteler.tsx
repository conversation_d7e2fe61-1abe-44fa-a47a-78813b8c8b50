/* eslint-disable react-native/no-inline-styles */
import React, { useEffect, useCallback } from 'react';
import {
    View, Text, StyleSheet, Animated, TouchableOpacity,
    ScrollView,
    Alert,
    Easing,
    PermissionsAndroid,
    Platform
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { launchCamera, launchImageLibrary } from 'react-native-image-picker';
import Geolocation from '@react-native-community/geolocation';
import FastImage from 'react-native-fast-image'
import { Image as ImageCompressor } from 'react-native-compressor';
import { useNavigation } from '@react-navigation/native';

import { apiurls } from '../../../../lib/contants.js';

import { stylex } from '../../../../lib/styles.dealerAktivite.js';
import { useUser } from '../../../../contexts/UserContext.tsx';

import { AsyncStorage, dbLocalTables, avgLifeTimeDays } from '../../../../lib/fnx.storage.js';
import ActivityFormModal from './ActivityFormModal.tsx';
import ImageViewerModal from './ImageViewerModal.tsx';

// Import navigation types
import type { StackNavigationProp } from '@react-navigation/stack';
type ProfileStackParamList = {
  AllActivitiesPage: {
    activities: any[];
    dealerName: string;
  };
  ActivityDetailScreen: {
    activity: any;
    dealerName: string;
  };
};
type NavigationProp = StackNavigationProp<ProfileStackParamList>;


interface RenderAktivitelerProps {
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
    dealer?: any;
    refreshContent?: any;
    updateMe?: any;
    copyToClipboard: (text: string, label: string) => void;
}

const RenderAktiviteler: React.FC<RenderAktivitelerProps> = (props) => {
    const {
        dealer,
        refreshContent,
        fadeAnim,
        slideAnim
    } = props;

    let storageName = dbLocalTables.entity_dealer + '_activities' + ':' + (dealer?.id || 'anon');
    const { userInfo } = useUser();
    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);
    const navigation = useNavigation<NavigationProp>();

    // Form states
    const [activityType, setActivityType] = React.useState<string>('ziyaret');
    const [activityNotes, setActivityNotes] = React.useState<string>('');
    const [selectedImages, setSelectedImages] = React.useState<any[]>([]);
    const [userLocation, setUserLocation] = React.useState<{ latitude: number; longitude: number } | null>(null);
    const [isGettingLocation, setIsGettingLocation] = React.useState<boolean>(false);

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
    };

    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(1)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(1)).current;
    const [modalVisible, setModalVisible] = React.useState(false);

    const [isSaving, setIsSaving] = React.useState(false);
    // Handle modal open with animation
    const openModal = () => {
        // console.log('Opening modal...', dealer);
        // Set initial animation values for entrance
        modalScaleAnim.setValue(0.8);
        modalOpacityAnim.setValue(0);

        setModalVisible(true);
        // console.log('Modal visible set to true');

        // Get user location when modal opens
        getUserLocation();

        // Start entrance animation immediately
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            })
        ]).start(() => {
            console.log('Animation completed');
        });
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setModalVisible(false); 
        });
    };

    const handleCancel = () => {
        // Reset form
        setActivityType('ziyaret');
        setActivityNotes('');
        setSelectedImages([]);
        setUserLocation(null);
        closeModal();
    };

    // Get user location using @react-native-community/geolocation
    const getUserLocation = async () => {
        setIsGettingLocation(true);

        try {
            // Request permission for Android
            if (Platform.OS === 'android') {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        title: 'Konum İzni',
                        message: 'Aktivite kaydı için konumunuza erişmek istiyoruz.',
                        buttonNeutral: 'Daha Sonra',
                        buttonNegative: 'İptal',
                        buttonPositive: 'Tamam',
                    }
                );

                if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
                    Alert.alert('Uyarı', 'Konum izni verilmedi');
                    setIsGettingLocation(false);
                    return;
                }
            }

            // Get current position using @react-native-community/geolocation
            Geolocation.getCurrentPosition(
                (position) => {
                    setUserLocation({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude,
                    });
                    setIsGettingLocation(false);
                },
                (error) => {
                    console.log('Location error:', error);
                    Alert.alert('Hata', 'Konum alınamadı. Lütfen GPS\'inizi açın.');
                    setIsGettingLocation(false);
                },
                { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
            );
        } catch (error) {
            console.log('Permission error:', error);
            setIsGettingLocation(false);
        }
    };

    // Image picker functions
    const handleTakePhoto = () => {
        launchCamera(
            {
                mediaType: 'photo',
                quality: 0.8,
                saveToPhotos: true,
            },
            (response) => {
                if (response.didCancel) {
                    console.log('User cancelled camera');
                } else if (response.errorCode) {
                    console.log('Camera Error: ', response.errorMessage);
                    Alert.alert('Hata', 'Kamera açılamadı');
                } else if (response.assets && response.assets.length > 0) {
                    const newImages = response.assets.map(asset => ({
                        uri: asset.uri,
                        fileName: asset.fileName,
                        type: asset.type,
                    }));
                    setSelectedImages([...selectedImages, ...newImages]);
                }
            }
        );
    };

    const handleSelectFromGallery = () => {
        launchImageLibrary(
            {
                mediaType: 'photo',
                quality: 0.8,
                selectionLimit: 10,
            },
            (response) => {
                if (response.didCancel) {
                    console.log('User cancelled image picker');
                } else if (response.errorCode) {
                    console.log('ImagePicker Error: ', response.errorMessage);
                    Alert.alert('Hata', 'Galeri açılamadı');
                } else if (response.assets && response.assets.length > 0) {
                    const newImages = response.assets.map(asset => ({
                        uri: asset.uri,
                        fileName: asset.fileName,
                        type: asset.type,
                    }));
                    setSelectedImages([...selectedImages, ...newImages]);
                }
            }
        );
    };

    const handleRemoveImage = (index: number) => {
        const newImages = selectedImages.filter((_, i) => i !== index);
        setSelectedImages(newImages);
    };

    const createFormData = (photos: any[], body: Record<string, any> = {}) => {
        const data = new FormData();

        if (Array.isArray(photos)) {
            if (photos.length > 1) {
                for (var i = 0; i < photos.length; i++) {
                    const photo = photos[i];
                    data.append('media', {
                        name: photo?.fileName,
                        type: photo?.type,
                        uri:
                            Platform.OS === 'ios'
                                ? photo?.uri.replace('file://', '')
                                : photo?.uri,
                    });
                }
            } else {
                data.append('media', {
                    name: photos[0]?.fileName,
                    type: photos[0]?.type,
                    uri:
                        Platform.OS === 'ios'
                            ? photos[0]?.uri.replace('file://', '')
                            : photos[0]?.uri,
                });
            }
        }

        Object.keys(body).forEach(key => {
            data.append(key, body[key]);
        });

        return data;
    };

    const handleSave = async () => {
        // Validate required fields
        if (!activityNotes.trim()) {
            Alert.alert('Uyarı', 'Lütfen aktivite notlarını girin', [{ text: 'Tamam' }]);
            return;
        }

        // Prepare dealer data
        const dealerData = {
            id: dealer?.id,
            tabelaAdi: dealer?.tabelaAdi,
            unvan: dealer?.unvan,
            kod: dealer?.kod,
            sehir: dealer?.konum?.sehir,
            ilce: dealer?.konum?.ilce,
            adres: dealer?.konum?.adres,
        };

        // Prepare user data
        const userData = {
            id: userInfo?.user?.id,
            name: userInfo?.user?.name,
            email: userInfo?.user?.email,
        };

        let postData = {
            activityType,
            activityNotes,
            images: selectedImages,
            dealer: JSON.stringify(dealerData),
            user: JSON.stringify(userData),
            userLocation: JSON.stringify(userLocation),
            timestamp: new Date().toISOString(),
        };

        // Compress images before upload
        let compressedImages = [];
        if (selectedImages && selectedImages.length > 0) {
            try {
                for (const image of selectedImages) {
                    const compressedUri = await ImageCompressor.compress(image.uri, {
                        compressionMethod: 'auto',
                        quality: 0.8,
                    });
                    compressedImages.push({
                        ...image,
                        uri: compressedUri,
                    });
                }
            } catch (error) {
                console.log('Image compression error:', error);
                // If compression fails, use original images
                compressedImages = selectedImages;
            }
        }

        const data2Post = createFormData(compressedImages, postData);
        // console.log('handleSubmit data2Post', JSON.stringify(data2Post));

        setIsSaving(true);
 
        const uri = apiurls.getdealers_activities;
        console.log('uri', uri);
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    // 'Content-Type': 'application/json',
                    'Content-Type': 'multipart/form-data',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
                body: data2Post,
                // body: JSON.stringify(postData),
            });
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }
            const data = await res.json();
            setIsSaving(false);
            console.log('activity saved', data);
            // Close modal after saving

            setActivityType('ziyaret');
            setActivityNotes('');
            setSelectedImages([]);
            setUserLocation(null);

            closeModal();
            refreshContent && refreshContent();
        } catch (error) {
            console.log('fetch error', error);
            setIsSaving(false);
            Alert.alert('Hata', 'Kontak kaydedilemedi', [{ text: 'Tamam' }]);
        } 
    };

    const [isDeleting] = React.useState(false);
    const refreshOverlayOpacity = React.useRef(new Animated.Value(0)).current;
    const refreshOverlayScale = React.useRef(new Animated.Value(0.8)).current;
    const spinValue = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        if (isSaving || isDeleting) {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(refreshOverlayScale, {
                    toValue: 1,
                    speed: 10,
                    useNativeDriver: true,
                }),
            ]).start();

            // Start spinning animation
            Animated.loop(
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1000,
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        } else {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(refreshOverlayScale, {
                    toValue: 0.8,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [isSaving, isDeleting, spinValue, refreshOverlayOpacity, refreshOverlayScale]);

    // Image modal state
    const [imageModalVisible, setImageModalVisible] = React.useState(false);
    const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);
    const [selectedActivity, setSelectedActivity] = React.useState<any>(null);

    const openImageModal = (activity: any, index: number) => {
        setSelectedActivity(activity);
        setSelectedImageIndex(index);
        setImageModalVisible(true);
    };

    const closeImageModal = () => {
        setImageModalVisible(false);
    };

    const [loading, setLoading] = React.useState<boolean>(false);
    const [activities, setActivities] = React.useState<any[]>([]);
    const getactivities_live = useCallback(async () => {
        try {
            const uri = apiurls.getdealers_activities + '?dealerId=' + dealer?.id;
            const res = await fetch(uri, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
            });
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }

            const data = await res.json();
            console.log('live activities data', data)
            return data;
        } catch (error) {
            console.log('fetch error', error);
            return false;
        }
    }, [userInfo?.token, dealer]);

    const getactivities = useCallback(async (force = false) => {
        console.log('getactivities')
        if (!force) {
            let data;
            try {
                data = await AsyncStorage.getItemLimited(storageName); 
                if (Array.isArray(data?.data) && data?.data.length > 0) { 
                    setActivities(data?.data);
                    setLoading(false);
                }
                else {
                    data = await getactivities_live();
                    await AsyncStorage.setItemLimitedwSec(
                        storageName,
                        data,
                        avgLifeTimeDays * 60 * 60 * 24,
                    );
                    setActivities(data?.data);
                    setLoading(false); 
                }
                console.log('!data', data)

            } catch (e) {
                if ((e as any)?.code && (e as any)?.code === 'nostoragedata') {
                    data = await getactivities_live();
                    if (data) {
                        await AsyncStorage.setItemLimitedwSec(
                            storageName,
                            data,
                            avgLifeTimeDays * 60 * 60 * 24,
                        );
                        setActivities(data?.data);
                    } else {
                        setActivities([]);
                    }
                }
            }
        } else {
            let data = await getactivities_live();
            console.log('data', data)
            if (data) {
                await AsyncStorage.setItemLimitedwSec(
                    storageName,
                    data,
                    avgLifeTimeDays * 60 * 60 * 24,
                );
                setActivities(data?.data);
            } else {
                console.log('no data ?', data)
                setActivities([]);
            }
        }
        setLoading(false);
    }, [getactivities_live, storageName]);

    const [updateMeLocal, setUpdateMeLocal] = React.useState(0);

    useEffect(() => {
        // getactivities(true);
        // console.log('what ?', props.updateMe, updateMeLocal);
        if (props.updateMe !== updateMeLocal) {
            // console.log('props refreshing', props.updateMe)
            setUpdateMeLocal(props.updateMe);
            getactivities(true);
        } else {
            // console.log('here')
            props.updateMe === 0 && updateMeLocal === 0 && getactivities()
        }

    }, [userInfo?.token, getactivities, props.updateMe, updateMeLocal]);


    return (
        <>
            <Animated.View style={[styles.card, {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
            }]}>
                <View style={[styles.cardHeader, { justifyContent: 'space-between', alignItems: 'center' }]}>
                    <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}
                        onPress={() => toggleContactCollapse()}
                    >
                        <Lucide name="calendar" size={20} color="#6B4EFF" />
                        <Text style={styles.cardTitle}>Aktivite ve Ziyaretler</Text>
                        <Lucide
                            name={!isCollapsed ? "chevron-down" : "chevron-up"}
                            size={20}
                            color="#666"
                        />
                    </TouchableOpacity>
                    <View>
                        <TouchableOpacity style={styles.addButton} onPress={openModal}>
                            <Lucide name="plus" size={14} color="#6B4EFF" />
                            <Text style={styles.addButtonText}>Yeni</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                {/* <TouchableOpacity style={styles.cardHeader} onPress={() => toggleContactCollapse()}>
                <Lucide name="calendar" size={20} color="#6B4EFF" />
                <Text style={styles.cardTitle}>Aktivite ve Ziyaretler</Text>
                <Lucide
                    name={!isCollapsed ? "chevron-down" : "chevron-up"}
                    size={20}
                    color="#666"
                />
            </TouchableOpacity> */}
                {isCollapsed && (
                    <View style={styles.cardContent}>
                        {loading && <Text style={styles.comingSoon}>Yükleniyor...</Text>}
                        {!loading && Array.isArray(activities) && activities.length === 0 && (
                            <Text style={styles.comingSoon}>Henüz aktivite eklenmemiş...</Text>
                        )}
                        {!loading && Array.isArray(activities) && activities.length > 0 && (
                            <View style={styles.activityCardsContainer}>
                                {activities.slice(0, 5).map((activity: any) => (
                                    <View key={activity.id} style={styles.activityCard}>
                                        <TouchableOpacity
                                            onPress={() => navigation.navigate('ActivityDetailScreen', {
                                                activity: activity,
                                                dealerName: dealer?.tabelaAdi || dealer?.ticariUnvan || 'Acente'
                                            })} style={styles.activityHeader}>
                                            {activity.activityType === 'ziyaret' && <Lucide name="map-pin" size={16} color="#6B4EFF" />}
                                            {activity.activityType === 'email' && <Lucide name="mail" size={16} color="#6B4EFF" />}
                                            {activity.activityType === 'telefon' && <Lucide name="phone" size={16} color="#6B4EFF" />}
                                            <Text style={styles.activityType}>
                                                {activity.activityType.charAt(0).toUpperCase() + activity.activityType.slice(1)}
                                            </Text>
                                            <Text style={styles.activityDate}>
                                                {activity?.user?.name} @ 
                                                {new Date(activity.createdAt).toLocaleDateString('tr-TR', {
                                                    day: 'numeric',
                                                    month: 'long',
                                                    year: 'numeric'
                                                })}
                                            </Text>
                                        </TouchableOpacity>
                                        <TouchableOpacity 
                                            onPress={() => navigation.navigate('ActivityDetailScreen', { 
                                                activity: activity, 
                                                dealerName: dealer?.tabelaAdi || dealer?.ticariUnvan || 'Acente' 
                                            })}
                                        >
                                            <Text style={styles.activityNotes}>{activity.activityNotes}</Text>
                                        </TouchableOpacity>
                                        
                                        {activity.media?.length > 0 && (
                                            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.mediaContainer}>
                                                {activity.media.map((media: any, index: number) => (
                                                    // <Image
                                                    //     key={index}
                                                    //     source={{ uri: media.s3FileUrl }}
                                                    //     style={styles.mediaThumbnail}
                                                    // />
                                                    <TouchableOpacity
                                                        key={index}
                                                        onPress={() => openImageModal(activity, index)}
                                                    >
                                                        <FastImage
                                                            style={styles.mediaThumbnail as any}
                                                            // style={{ width: 200, height: 200 }}
                                                            source={{

                                                                uri: media.s3FileUrl,
                                                                // headers: { Authorization: 'someAuthToken' },
                                                                priority: FastImage.priority.normal,
                                                            }}
                                                            resizeMode={FastImage.resizeMode.contain}
                                                        />
                                                    </TouchableOpacity>

                                                ))}
                                            </ScrollView>
                                        )}
                                    </View>
                                ))}
                                
                                {activities.length > 5 && (
                                    <TouchableOpacity 
                                        style={styles.showMoreButton}
                                        onPress={() => navigation.navigate('AllActivitiesPage', { 
                                            activities: activities, 
                                            dealerName: dealer?.tabelaAdi || dealer?.ticariUnvan || 'Acente' 
                                        })}
                                    >
                                        <Text style={styles.showMoreText}>Daha fazlasını gör</Text>
                                        <Lucide name="arrow-right" size={14} color="#6B4EFF" />
                                    </TouchableOpacity>
                                )}
                            </View>
                        )}
                    </View>
                )}
            </Animated.View>

            <ActivityFormModal
                modalVisible={modalVisible}
                isSaving={isSaving}
                isDeleting={isDeleting}
                activityType={activityType}
                activityNotes={activityNotes}
                selectedImages={selectedImages}
                userLocation={userLocation}
                isGettingLocation={isGettingLocation}
                modalOpacityAnim={modalOpacityAnim}
                modalScaleAnim={modalScaleAnim}
                refreshOverlayOpacity={refreshOverlayOpacity}
                refreshOverlayScale={refreshOverlayScale}
                spinValue={spinValue}
                setActivityType={setActivityType}
                setActivityNotes={setActivityNotes}
                handleTakePhoto={handleTakePhoto}
                handleSelectFromGallery={handleSelectFromGallery}
                handleRemoveImage={handleRemoveImage}
                getUserLocation={getUserLocation}
                handleSave={handleSave}
                handleCancel={handleCancel}
            />

            <ImageViewerModal
                imageModalVisible={imageModalVisible}
                selectedActivity={selectedActivity}
                selectedImageIndex={selectedImageIndex}
                closeImageModal={closeImageModal}
            />
        </>
    );
};

const styles = StyleSheet.create({
    ...stylex as any, // Temporary type assertion to resolve multiple errors
    cardHeader: {
        flexDirection: 'row' as 'row',
        alignItems: 'center' as 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#EEE',
    },
    showMoreButton: {
        flexDirection: 'row',
        alignItems: 'center', // Changed from string to valid FlexAlignType
        justifyContent: 'center',
        gap: 8,
        padding: 12,
        backgroundColor: '#F8F8F8',
        borderRadius: 30,
        marginTop: 16,
        borderWidth: 1,
        borderColor: '#EEE',
    },
    // Yeni stiller (yinelenenler kaldırıldı)
    activityHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
    },
    activityDate: {
        fontSize: 12,
        color: '#666',
        marginLeft: 0,
    },
});

export default RenderAktiviteler;
