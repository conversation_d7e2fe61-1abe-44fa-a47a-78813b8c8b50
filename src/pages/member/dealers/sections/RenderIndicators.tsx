import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Animated } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

interface RenderIndicatorsProps {
    createdAt?: string;
    updatedAt?: string;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}

const RenderIndicators: React.FC<RenderIndicatorsProps> = ({
    createdAt,
    updatedAt,
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {

    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(true);
    // const contactAnimations = React.useRef(new Animated.Value(1)).current;

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);

        // Animated.timing(contactAnimations, {
        //     toValue: newIsCollapsed ? 0 : 1,
        //     duration: 300,
        //     useNativeDriver: false,
        // }).start();
    };

    return (
        <Animated.View style={[styles.card, {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
        }]}>
            <TouchableOpacity style={styles.cardHeader} onPress={() => toggleContactCollapse()} >
                <Lucide name="activity" size={20} color="#6B4EFF" />
                <Text style={styles.cardTitle}>Performans Göstergeleri</Text>
                <Lucide
                    name={!isCollapsed ? "chevron-down" : "chevron-up"}
                    size={20}
                    color="#666"
                />
            </TouchableOpacity>
            {isCollapsed && (
                <View style={styles.cardContent}>
                    {createdAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="plus" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Son 30 gün rezervasyon:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(createdAt).toLocaleDateString('tr-TR'),
                                'Oluşturulma tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(createdAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                    {updatedAt && (
                        <View style={styles.infoRow}>
                            <Lucide name="refresh-cw" size={16} color="#666" />
                            <Text style={styles.infoLabel}>Güncelleme:</Text>
                            <TouchableOpacity onLongPress={() => copyToClipboard(
                                new Date(updatedAt).toLocaleDateString('tr-TR'),
                                'Güncelleme tarihi'
                            )}>
                                <Text style={styles.infoValue}>
                                    {new Date(updatedAt).toLocaleDateString('tr-TR')}
                                </Text>
                            </TouchableOpacity>
                        </View>
                    )}
                </View>
            )}
        </Animated.View>
    );
};

// Reuse existing card styles from other components
const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },
    infoRow: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 12,
    },
    infoLabel: {
        fontSize: 14,
        color: '#666',
        marginLeft: 12,
        flex: 1,
    },
    infoValue: {
        fontSize: 14,
        color: '#333',
        fontWeight: '500',
        flex: 2,
        textAlign: 'right',
    },
});

export default RenderIndicators;