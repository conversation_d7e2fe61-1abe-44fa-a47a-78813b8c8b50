import React from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';


import RefreshOverlay from '../List.RefreshOverlay.tsx';
import { useUser } from '../../../../contexts/UserContext.tsx';

import { apiurls } from '../../../../lib/contants.js';
import { stylex } from '../../../../lib/styles.dealers.js';

interface RenderProspectStagesProps {
    dealer?: any;
    refreshContent?: any;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}
const RenderProspectStages: React.FC<RenderProspectStagesProps> = ({
    dealer,
    refreshContent,
    // updatedAt parameter removed as it's no longer used
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    const { userInfo } = useUser();
    const { refData } = dealer;
     
    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);
    // const contactAnimations = React.useRef(new Animated.Value(1)).current;

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
        console.log('dealer', dealer);
        // Animated.timing(contactAnimations, {
        //     toValue: newIsCollapsed ? 0 : 1,
        //     duration: 300,
        //     useNativeDriver: false,
        // }).start();
    };

    return (
        <Animated.View style={[styles.card, {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
        }]}>
            <View style={styles.cardHeader}>
                <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', gap: 2, }} onPress={() => toggleContactCollapse()} >
                    <Lucide name="workflow" size={20} color="#6B4EFF" />
                    <Text style={styles.cardTitle}>Aday {'-->'} Bayi</Text>
                    <Lucide
                        name={!isCollapsed ? "chevron-down" : "chevron-up"}
                        size={20}
                        color="#666"
                    />
                </TouchableOpacity>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 10, marginLeft: 'auto' }}>
                    {dealer?.stageCode && (<Text style={styles.infoValue_s}>{dealer.stageCode}</Text>)}
                    {dealer?.durum && (<Text style={[styles.infoValue_s, dealer.durum === 'aktif' ? styles.activeStatus : styles.inactiveStatus]}>
                        {dealer.durum}
                    </Text>)}
                </View>
            </View>
            {isCollapsed && (
                <View style={styles.cardContent}>
                    <Text style={styles.comingSoon}>Yakında gelecek...</Text>
                    <Text style={styles.comingSoon}>{JSON.stringify(dealer)}</Text>
                </View>
            )}
        </Animated.View>
    );
};


// @ts-ignore
const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
        minHeight: 100,
        justifyContent: 'center',
    },
    comingSoon: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
    },
    ...stylex,
});

export default RenderProspectStages;