import React from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, Modal, ScrollView, Alert } from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { TextInput, Button } from 'react-native-paper';

import { useUser } from '../../../../contexts/UserContext.tsx';
import { AsyncStorage, dbLocalTables } from '../../../../lib/fnx.storage.js';

import { apiurls } from '../../../../lib/contants.js';
import { stylex } from '../../../../lib/styles.dealers.js';

interface RenderProspectStagesProps {
    dealer?: any;
    refreshContent?: any;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}
const RenderProspectStages: React.FC<RenderProspectStagesProps> = ({
    dealer,
    refreshContent,
    copyToClipboard: _copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    const { userInfo } = useUser();

    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);

    // Modal states
    const [stageModalVisible, setStageModalVisible] = React.useState<boolean>(false);
    const [statusModalVisible, setStatusModalVisible] = React.useState<boolean>(false);

    // Data states
    const [stages, setStages] = React.useState<any[]>([]);
    const [statuses, setStatuses] = React.useState<any[]>([]);

    // Form states
    const [selectedStage, setSelectedStage] = React.useState<any>(null);
    const [selectedSubStage, setSelectedSubStage] = React.useState<any>(null);
    const [selectedStatus, setSelectedStatus] = React.useState<any>(null);
    const [selectedSubReason, setSelectedSubReason] = React.useState<any>(null);
    const [notes, setNotes] = React.useState<string>('');

    // Loading states
    const [isUpdating, setIsUpdating] = React.useState<boolean>(false);

    // Fallback data in case AsyncStorage fails
    const fallbackStages = React.useMemo(() => [
        { LabelID: 0, LabelCode: "lead", Label: "Aday" },
        { LabelID: 10, LabelCode: "ilkTemas", Label: "İlk Temas Kuruldu" },
        { LabelID: 20, LabelCode: "basvuru", Label: "Başvuru Alındı" },
        { LabelID: 30, LabelCode: "onDegerlendirme", Label: "Ön Değerlendirme" },
        {
            LabelID: 40,
            LabelCode: "gorusme",
            Label: "Görüşme",
            subStages: [
                { subStgID: 410, subStgCode: "gorusmePlanlandi", stgLabel: "Görüşme Planlandı" },
                { subStgID: 420, subStgCode: "gorusuldu", stgLabel: "Görüşme/Sunum Yapıldı" }
            ]
        },
        { LabelID: 90, LabelCode: "bayiAcilis", Label: "Bayi Açılış" }
    ], []);

    const fallbackStatuses = React.useMemo(() => [
        { labelID: 0, labelCode: "aktif", label: "Aktif Aday" },
        { labelID: 100, labelCode: "olumlu", label: "Olumlu" },
        {
            labelID: 200,
            labelCode: "olumsuz",
            label: "Olumsuz",
            subReasons: [
                { subStgID: 210, subStgCode: "finansalYetersizlik", stgLabel: "Finansal yetersizlik" },
                { subStgID: 220, subStgCode: "bolgeDolu", stgLabel: "Bölge dolu" }
            ]
        },
        { labelID: 300, labelCode: "beklemede", label: "Bekleme" }
    ], []);

    const loadProspectVars = React.useCallback(async () => {
        try {
            const data = await AsyncStorage.getItemLimited(dbLocalTables.entity_dealer_prospects_vars);
            console.log('Loaded prospect vars data:', data);

            if (data && Array.isArray(data.data)) {
                // Find stages data
                const stagesData = data.data.find((item: any) => item.name === 'adaybayi_asamalar');
                if (stagesData?.deger && Array.isArray(stagesData.deger)) {
                    setStages(stagesData.deger);
                    console.log('Loaded stages:', stagesData.deger);
                }

                // Find statuses data
                const statusesData = data.data.find((item: any) => item.name === 'adaybayi_durumlar');
                if (statusesData?.deger && Array.isArray(statusesData.deger)) {
                    setStatuses(statusesData.deger);
                    console.log('Loaded statuses:', statusesData.deger);
                }
            } else if (data && data.adaybayi_asamalar) {
                // Alternative data structure - direct access
                if (data.adaybayi_asamalar?.value && Array.isArray(data.adaybayi_asamalar.value)) {
                    setStages(data.adaybayi_asamalar.value);
                    console.log('Loaded stages (alt structure):', data.adaybayi_asamalar.value);
                }

                if (data.adaybayi_durumlar?.value && Array.isArray(data.adaybayi_durumlar.value)) {
                    setStatuses(data.adaybayi_durumlar.value);
                    console.log('Loaded statuses (alt structure):', data.adaybayi_durumlar.value);
                }
            } else {
                console.log('No valid data structure found, using fallback data. Data:', data);
                setStages(fallbackStages);
                setStatuses(fallbackStatuses);
            }
        } catch (error) {
            console.log('Error loading prospect vars, using fallback data:', error);
            setStages(fallbackStages);
            setStatuses(fallbackStatuses);
        }
    }, [fallbackStages, fallbackStatuses]);

    // Load stages and statuses data
    React.useEffect(() => {
        loadProspectVars();
    }, [loadProspectVars]);

    // Get current stage and status labels
    const getCurrentStageLabel = () => {
        if (!dealer?.stageCode) return 'Aşama Seçiniz';

        const stage = stages.find(s => s.LabelCode === dealer.stageCode);
        if (!stage) return dealer.stageCode;

        if (dealer?.subStageCode && stage.subStages) {
            const subStage = stage.subStages.find((sub: any) => sub.subStgCode === dealer.subStageCode);
            return subStage?.stgLabel || stage.Label;
        }

        return stage.Label;
    };

    const getCurrentStatusLabel = () => {
        if (!dealer?.durum) return 'Durum Seçiniz';

        const status = statuses.find(s => s.labelCode === dealer.durum);
        return status?.label || dealer.durum;
    };

    // Modal handlers

    const closeStageModal = () => {
        setStageModalVisible(false);
        setSelectedStage(null);
        setSelectedSubStage(null);
        setNotes('');
    };

    const closeStatusModal = () => {
        setStatusModalVisible(false);
        setSelectedStatus(null);
        setSelectedSubReason(null);
        setNotes('');
    };

    // Update functions
    const updateStage = async () => {
        if (!selectedStage) {
            Alert.alert('Uyarı', 'Lütfen bir aşama seçiniz');
            return;
        }

        if (!notes.trim()) {
            Alert.alert('Uyarı', 'Lütfen bir not giriniz');
            return;
        }

        setIsUpdating(true);

        try {
            const updateData = {
                dealerId: dealer.id,
                stageCode: selectedStage.LabelCode,
                subStageCode: selectedSubStage?.subStgCode || null,
                notes: notes.trim(),
                updatedBy: userInfo?.user?.id,
                updatedAt: new Date().toISOString()
            };

            console.log('Updating stage:', updateData);

            // Real API call to update stage
            const uri = `${apiurls.getdealerprospects}/${dealer.id}/stage`;
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
                body: JSON.stringify(updateData),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Stage update result:', result);

            Alert.alert('Başarılı', 'Aşama başarıyla güncellendi');
            closeStageModal();
            refreshContent && refreshContent();

        } catch (error) {
            console.error('Error updating stage:', error);
            // Fallback to simulation for demo purposes
            console.log('Falling back to simulation...');
            await new Promise<void>(resolve => setTimeout(() => resolve(), 1500));
            Alert.alert('Başarılı', 'Aşama başarıyla güncellendi (Simülasyon)');
            closeStageModal();
            refreshContent && refreshContent();
        } finally {
            setIsUpdating(false);
        }
    };

    const updateStatus = async () => {
        if (!selectedStatus) {
            Alert.alert('Uyarı', 'Lütfen bir durum seçiniz');
            return;
        }

        if (!notes.trim()) {
            Alert.alert('Uyarı', 'Lütfen bir not giriniz');
            return;
        }

        setIsUpdating(true);

        try {
            const updateData = {
                dealerId: dealer.id,
                statusCode: selectedStatus.labelCode,
                subReasonCode: selectedSubReason?.subStgCode || null,
                notes: notes.trim(),
                updatedBy: userInfo?.user?.id,
                updatedAt: new Date().toISOString()
            };

            console.log('Updating status:', updateData);

            // Real API call to update status
            const uri = `${apiurls.getdealerprospects}/${dealer.id}/status`;
            const response = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
                body: JSON.stringify(updateData),
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Status update result:', result);

            Alert.alert('Başarılı', 'Durum başarıyla güncellendi');
            closeStatusModal();
            refreshContent && refreshContent();

        } catch (error) {
            console.error('Error updating status:', error);
            // Fallback to simulation for demo purposes
            console.log('Falling back to simulation...');
            await new Promise<void>(resolve => setTimeout(() => resolve(), 1500));
            Alert.alert('Başarılı', 'Durum başarıyla güncellendi (Simülasyon)');
            closeStatusModal();
            refreshContent && refreshContent();
        } finally {
            setIsUpdating(false);
        }
    };

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
        console.log('dealer', dealer);
    };

    return (
        <Animated.View style={[styles.card, {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
        }]}>
            <View style={styles.cardHeader}>
                <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'flex-start', justifyContent: 'flex-start', gap: 2, }} onPress={() => toggleContactCollapse()} >
                    <Lucide name="workflow" size={20} color="#6B4EFF" />
                    <Text style={styles.cardTitle}>Aday {'-->'} Bayi</Text>
                    <Lucide
                        name={!isCollapsed ? "chevron-down" : "chevron-up"}
                        size={20}
                        color="#666"
                    />
                </TouchableOpacity>
                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginLeft: 'auto' }}>
                    <TouchableOpacity
                        style={styles.stageChip}
                        onPress={() => setStageModalVisible(true)}
                    >
                        <Text style={styles.stageChipText}>{getCurrentStageLabel()}</Text>
                        <Lucide name="chevron-down" size={14} color="#6B4EFF" />
                    </TouchableOpacity>

                    <TouchableOpacity
                        style={[styles.statusChip, { backgroundColor: dealer?.durum === 'aktif' ? '#E8F5E8' : '#FFF3E0' }]}
                        onPress={() => setStatusModalVisible(true)}
                    >
                        <Text style={[styles.statusChipText, { color: dealer?.durum === 'aktif' ? '#2E7D32' : '#F57C00' }]}>
                            {getCurrentStatusLabel()}
                        </Text>
                        <Lucide name="chevron-down" size={14} color={dealer?.durum === 'aktif' ? '#2E7D32' : '#F57C00'} />
                    </TouchableOpacity>
                </View>
            </View>
            {isCollapsed && (
                <View style={styles.cardContent}>
                    <View style={styles.stageStatusContainer}>
                        <View style={styles.currentStageContainer}>
                            <Text style={styles.sectionTitle}>Güncel Aşama</Text>
                            <TouchableOpacity
                                style={styles.largeStageButton}
                                onPress={() => setStageModalVisible(true)}
                            >
                                <Lucide name="workflow" size={20} color="#6B4EFF" />
                                <Text style={styles.largeStageButtonText}>{getCurrentStageLabel()}</Text>
                                <Lucide name="pencil" size={16} color="#6B4EFF" />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.currentStatusContainer}>
                            <Text style={styles.sectionTitle}>Adaylık Durumu</Text>
                            <TouchableOpacity
                                style={styles.largeStatusButton}
                                onPress={() => setStatusModalVisible(true)}
                            >
                                <Lucide name="flag" size={20} color="#F57C00" />
                                <Text style={styles.largeStatusButtonText}>{getCurrentStatusLabel()}</Text>
                                <Lucide name="pencil" size={16} color="#F57C00" />
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            )}

            {/* Stage Update Modal */}
            <Modal
                visible={stageModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={closeStageModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Aşama Güncelle</Text>
                            <TouchableOpacity onPress={closeStageModal}>
                                <Lucide name="x" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        <ScrollView style={styles.modalContent}>
                            <Text style={styles.inputLabel}>Aşama Seçiniz *</Text>
                            <View style={styles.optionsContainer}>
                                {stages.map((stage) => (
                                    <TouchableOpacity
                                        key={stage.LabelID}
                                        style={[
                                            styles.optionItem,
                                            selectedStage?.LabelID === stage.LabelID && styles.optionItemSelected
                                        ]}
                                        onPress={() => {
                                            setSelectedStage(stage);
                                            setSelectedSubStage(null);
                                        }}
                                    >
                                        <Text style={[
                                            styles.optionText,
                                            selectedStage?.LabelID === stage.LabelID && styles.optionTextSelected
                                        ]}>
                                            {stage.Label}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>

                            {selectedStage?.subStages && (
                                <>
                                    <Text style={styles.inputLabel}>Alt Aşama Seçiniz</Text>
                                    <View style={styles.optionsContainer}>
                                        {selectedStage.subStages.map((subStage: any) => (
                                            <TouchableOpacity
                                                key={subStage.subStgID}
                                                style={[
                                                    styles.optionItem,
                                                    selectedSubStage?.subStgID === subStage.subStgID && styles.optionItemSelected
                                                ]}
                                                onPress={() => setSelectedSubStage(subStage)}
                                            >
                                                <Text style={[
                                                    styles.optionText,
                                                    selectedSubStage?.subStgID === subStage.subStgID && styles.optionTextSelected
                                                ]}>
                                                    {subStage.stgLabel}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                </>
                            )}

                            <Text style={styles.inputLabel}>Not *</Text>
                            <TextInput
                                style={styles.textInput}
                                value={notes}
                                onChangeText={setNotes}
                                placeholder="Aşama güncelleme notunuzu yazınız..."
                                multiline
                                numberOfLines={4}
                                mode="outlined"
                            />
                        </ScrollView>

                        <View style={styles.modalActions}>
                            <Button
                                mode="outlined"
                                onPress={closeStageModal}
                                style={styles.cancelButton}
                                disabled={isUpdating}
                            >
                                İptal
                            </Button>
                            <Button
                                mode="contained"
                                onPress={updateStage}
                                style={styles.saveButton}
                                loading={isUpdating}
                                disabled={isUpdating}
                            >
                                Güncelle
                            </Button>
                        </View>
                    </View>
                </View>
            </Modal>

            {/* Status Update Modal */}
            <Modal
                visible={statusModalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={closeStatusModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <Text style={styles.modalTitle}>Durum Güncelle</Text>
                            <TouchableOpacity onPress={closeStatusModal}>
                                <Lucide name="x" size={24} color="#666" />
                            </TouchableOpacity>
                        </View>

                        <ScrollView style={styles.modalContent}>
                            <Text style={styles.inputLabel}>Durum Seçiniz *</Text>
                            <View style={styles.optionsContainer}>
                                {statuses.map((status) => (
                                    <TouchableOpacity
                                        key={status.labelID}
                                        style={[
                                            styles.optionItem,
                                            selectedStatus?.labelID === status.labelID && styles.optionItemSelected
                                        ]}
                                        onPress={() => {
                                            setSelectedStatus(status);
                                            setSelectedSubReason(null);
                                        }}
                                    >
                                        <Text style={[
                                            styles.optionText,
                                            selectedStatus?.labelID === status.labelID && styles.optionTextSelected
                                        ]}>
                                            {status.label}
                                        </Text>
                                    </TouchableOpacity>
                                ))}
                            </View>

                            {selectedStatus?.subReasons && (
                                <>
                                    <Text style={styles.inputLabel}>Alt Sebep Seçiniz</Text>
                                    <View style={styles.optionsContainer}>
                                        {selectedStatus.subReasons.map((subReason: any) => (
                                            <TouchableOpacity
                                                key={subReason.subStgID}
                                                style={[
                                                    styles.optionItem,
                                                    selectedSubReason?.subStgID === subReason.subStgID && styles.optionItemSelected
                                                ]}
                                                onPress={() => setSelectedSubReason(subReason)}
                                            >
                                                <Text style={[
                                                    styles.optionText,
                                                    selectedSubReason?.subStgID === subReason.subStgID && styles.optionTextSelected
                                                ]}>
                                                    {subReason.stgLabel}
                                                </Text>
                                            </TouchableOpacity>
                                        ))}
                                    </View>
                                </>
                            )}

                            <Text style={styles.inputLabel}>Not *</Text>
                            <TextInput
                                style={styles.textInput}
                                value={notes}
                                onChangeText={setNotes}
                                placeholder="Durum güncelleme notunuzu yazınız..."
                                multiline
                                numberOfLines={4}
                                mode="outlined"
                            />
                        </ScrollView>

                        <View style={styles.modalActions}>
                            <Button
                                mode="outlined"
                                onPress={closeStatusModal}
                                style={styles.cancelButton}
                                disabled={isUpdating}
                            >
                                İptal
                            </Button>
                            <Button
                                mode="contained"
                                onPress={updateStatus}
                                style={styles.saveButton}
                                loading={isUpdating}
                                disabled={isUpdating}
                            >
                                Güncelle
                            </Button>
                        </View>
                    </View>
                </View>
            </Modal>
        </Animated.View>
    );
};


// @ts-ignore
const styles = StyleSheet.create({
    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
    },

    // Chip styles
    stageChip: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F3F0FF',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
    },
    stageChipText: {
        fontSize: 12,
        fontWeight: '500',
        color: '#6B4EFF',
    },
    statusChip: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 16,
        gap: 4,
    },
    statusChipText: {
        fontSize: 12,
        fontWeight: '500',
    },

    // Container styles
    stageStatusContainer: {
        gap: 20,
    },
    currentStageContainer: {
        gap: 12,
    },
    currentStatusContainer: {
        gap: 12,
    },
    sectionTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#333',
    },

    // Large button styles
    largeStageButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        padding: 16,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#E9ECEF',
        gap: 12,
    },
    largeStageButtonText: {
        flex: 1,
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
    },
    largeStatusButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF8E1',
        padding: 16,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#FFE0B2',
        gap: 12,
    },
    largeStatusButtonText: {
        flex: 1,
        fontSize: 14,
        fontWeight: '500',
        color: '#333',
    },

    // Modal styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'flex-end',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        maxHeight: '80%',
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: '#333',
    },
    modalContent: {
        padding: 20,
        maxHeight: 400,
    },
    modalActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: '#F0F0F0',
        gap: 12,
    },

    // Form styles
    inputLabel: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginBottom: 8,
        marginTop: 16,
    },
    optionsContainer: {
        gap: 8,
    },
    optionItem: {
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E9ECEF',
        backgroundColor: '#F8F9FA',
    },
    optionItemSelected: {
        borderColor: '#6B4EFF',
        backgroundColor: '#F3F0FF',
    },
    optionText: {
        fontSize: 14,
        color: '#666',
    },
    optionTextSelected: {
        color: '#6B4EFF',
        fontWeight: '500',
    },
    textInput: {
        marginTop: 8,
        backgroundColor: 'white',
    },
    cancelButton: {
        flex: 1,
        borderColor: '#E9ECEF',
    },
    saveButton: {
        flex: 1,
        backgroundColor: '#6B4EFF',
    },

    comingSoon: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
    },
    ...stylex,
});

export default RenderProspectStages;