/* eslint-disable react/no-unstable-nested-components */
/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { Linking } from 'react-native';
import {
    View,
    Text,
    TouchableOpacity,
    StyleSheet,
    Animated,
    TouchableWithoutFeedback,
    KeyboardAvoidingView,
    ScrollView,
    Platform,
    Modal,
    Alert,
    Easing,
    ActivityIndicator,
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';
import { TextInput, Button } from 'react-native-paper';
import BouncyCheckbox from "react-native-bouncy-checkbox";

import RefreshOverlay from '../List.RefreshOverlay.tsx';
import { useUser } from '../../../../contexts/UserContext.tsx';

import { apiurls } from '../../../../lib/contants.js';
import { stylex } from '../../../../lib/styles.dealers.js';

// TODO: dealer detay: view contact from dealer...
// TODO: dealer detay: add contact from existing contacts...
// TODO: add notes to contact.
// TODO: assign accountManager to dealer.

interface Contact {
    _id: string;
    fullName: string;
    email?: string;
    phone?: string;
    dealerData: {
        id: string;
        tabelaAdi: string;
        position: string;
        isAuthorized: boolean;
        dtCreated: string;
        dtUpdated: string;
        status: string;
    };
    position?: string;
    createdAt: string;
    updatedAt?: string;
}
interface RenderContactPeopleProps {
    dealer?: any;
    refreshContent?: any;
    copyToClipboard: (text: string, label: string) => void;
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
}
const RenderContactPeople: React.FC<RenderContactPeopleProps> = ({
    dealer,
    refreshContent,
    // updatedAt parameter removed as it's no longer used
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {
    const { userInfo } = useUser();
    const { refData } = dealer;
    const [modalVisible, setModalVisible] = React.useState(false);
    const [fullName, setFullName] = React.useState('');
    const [position, setPosition] = React.useState('');
    const [phone, setPhone] = React.useState('');
    const [email, setEmail] = React.useState('');
    const [contactId, setContactId] = React.useState('');
    const [isAuthorized, setIsAuthorized] = React.useState(false);
    const [isSaving, setIsSaving] = React.useState(false);
    const [isEditing, setIsEditing] = React.useState(false);
    const [isDeleting, setIsDeleting] = React.useState(false);

    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(1)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(1)).current;

    // Handle modal open with animation
    const openModal = () => {
        // console.log('Opening modal...', dealer);
        // Set initial animation values for entrance
        modalScaleAnim.setValue(0.8);
        modalOpacityAnim.setValue(0);

        setModalVisible(true);
        // console.log('Modal visible set to true');

        // Start entrance animation immediately
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            })
        ]).start(() => {
            console.log('Animation completed');
        });
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setModalVisible(false);
            // Reset form fields
            setContactId('');
            setFullName('');
            setPosition('');
            setPhone('');
            setEmail('');
            setIsAuthorized(false);
            setIsSaving(false);
            setIsEditing(false);
        });
    };

    const handleDelete = async () => {
        Alert.alert(
            'Silmek istediğinize emin misiniz ?',
            '',
            [
                {
                    text: 'Vazgeç',
                    onPress: () => setIsDeleting(false),
                    style: 'cancel',
                },
                {
                    text: 'Sil',
                    onPress: async () => {
                        setIsDeleting(true);
                        var uri = (apiurls.getdealers_contacts) + '/' + contactId + '?dealerId=' + dealer.id;
                        uri += dealer.refData && dealer.refData === 'adaybayi' ? '&refdata=' + dealer.refData : '';
                        console.log('delete uri', uri);
                        try {
                            setIsDeleting(true);
                            const res = await fetch(uri, {
                                method: 'DELETE',
                                headers: {
                                    'Content-Type': 'application/json',
                                    Authorization: `Bearer ${userInfo?.token}`,
                                },
                            });
                            if (!res.ok) {
                                throw new Error(`HTTP error! status: ${res.status}`);
                            }
                            const data = await res.json();
                            console.log('Contact deleted', data);
                            // Close modal after saving
                            closeModal();
                            refreshContent && refreshContent();
                        } catch (error) {
                            console.log('fetch error', error);
                            setIsDeleting(false);
                            Alert.alert('Hata', 'Kontak silinemedi', [{ text: 'Tamam' }]);
                        }
                        setIsDeleting(false);
                    },
                },
            ],
            { cancelable: false },
        );

    };

    const handleSave = async () => {
        // Validate required fields
        if (!fullName || !position || !phone || !email) {
            return;
        }
        let postData = {
            fullName,
            phone,
            email,
            dealer: {
                id: dealer.id,
                tabelaAdi: dealer.tabelaAdi,
                position,
                isAuthorized,
                dtCreated: new Date(Date.now()).toISOString(),
                status: 'active',
            },
        };

        setIsSaving(true);

        if (isEditing) {
            postData.dealer = {
                ...postData.dealer,
                dtUpdatedBy: userInfo?.user.id,
                dtUpdated: new Date(Date.now()).toISOString(),
            };

            var uri = apiurls.getdealers_contacts + '/' + contactId;
            uri += dealer.refData && dealer.refData === 'adaybayi' ? '?refdata=' + dealer.refData : '';
            console.log('edit uri', uri);
            try {
                const res = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${userInfo?.token}`,
                    },
                    body: JSON.stringify(postData),
                });
                if (!res.ok) {
                    console.log('res not ok', res);
                    const datax = await res.json();
                    console.log('datax', datax);
                    throw new Error(`HTTP error! status: ${res.status}`);
                }
                const data = await res.json();
                console.log('Contact saved', data);
                // Close modal after saving
                console.log('uri', uri, postData);

                closeModal();
                refreshContent && refreshContent();
            } catch (error) {
                console.log('fetch error', error);
                setIsSaving(false);
                Alert.alert('Hata', 'Kontak kaydedilemedi', [{ text: 'Tamam' }]);
            }

            closeModal();
            // postData = { ...postData, _id: dealer.id };

        } else {
            var uri = apiurls.getdealers_contacts;
            uri += dealer.refData && dealer.refData === 'adaybayi' ? '?refdata=' + dealer.refData : '';
            console.log('save uri', uri);
            // console.log('uri', uri);
            try {
                const res = await fetch(uri, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        Authorization: `Bearer ${userInfo?.token}`,
                    },
                    body: JSON.stringify(postData),
                });
                if (!res.ok) {
                    throw new Error(`HTTP error! status: ${res.status}`);
                }
                const data = await res.json();
                // console.log('Contact saved', data);
                // Close modal after saving
                closeModal();
                refreshContent && refreshContent();
            } catch (error) {
                console.log('fetch error', error);
                setIsSaving(false);
                Alert.alert('Hata', 'Kontak kaydedilemedi', [{ text: 'Tamam' }]);
            }
        }

        // Simulate saving with 2-second delay
        // await new Promise((resolve) => setTimeout(() => resolve(null), 2000));


    };

    const handleCancel = () => {
        closeModal();
    };

    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
    };

    const editContact = (contact: Contact) => {
        console.log('Editing contact', contact);
        setContactId(contact._id);
        setIsEditing(true);
        setFullName(contact?.fullName);
        setPosition(contact?.dealerData?.position!);
        setPhone(contact?.phone!);
        setEmail(contact?.email!);
        setIsAuthorized(contact?.dealerData?.isAuthorized || false);
        openModal();
    };

    const refreshOverlayOpacity = React.useRef(new Animated.Value(0)).current;
    const refreshOverlayScale = React.useRef(new Animated.Value(0.8)).current;
    const spinValue = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        if (isSaving || isDeleting) {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(refreshOverlayScale, {
                    toValue: 1,
                    speed: 10,
                    useNativeDriver: true,
                }),
            ]).start();

            // Start spinning animation
            Animated.loop(
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1000,
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        } else {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(refreshOverlayScale, {
                    toValue: 0.8,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [isSaving, isDeleting, spinValue]);


    return (
        <>
            <Animated.View style={[styles.card, {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
            }]}>
                <View style={[styles.cardHeader, { justifyContent: 'space-between', alignItems: 'center' }]}>
                    <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}
                        onPress={() => toggleContactCollapse()}
                    >
                        <Lucide name="users" size={20} color="#6B4EFF" />
                        <Text style={styles.cardTitle}>Kontaklar</Text>
                        <Lucide
                            name={!isCollapsed ? "chevron-down" : "chevron-up"}
                            size={20}
                            color="#666"
                        />
                    </TouchableOpacity>
                    <View>
                        <TouchableOpacity style={styles.addButton} onPress={openModal}>
                            <Lucide name="plus" size={14} color="#6B4EFF" />
                            <Text style={styles.addButtonText}>Yeni</Text>
                        </TouchableOpacity>
                    </View>
                </View>
                {isCollapsed && (
                    <View style={styles.cardContent}>
                        {dealer?.contacts?.length > 0 && (
                            <View style={styles.contactCardsContainer}>
                                {dealer.contacts.map((contact: Contact, index: number) => (
                                    <View key={contact._id + '_' + index} style={styles.contactCard}>
                                        {/* Header */}
                                        <View style={[styles.cardHeader, { padding: 0, justifyContent: 'space-between', }]}>
                                            {/* <Lucide name="user" size={20} color="#2F80ED" /> */}
                                            <View style={styles.headerTextContainer}>
                                                <View style={{ flexDirection: 'row', alignItems: 'center', gap: 4, }}>
                                                    {contact?.dealerData?.isAuthorized && (
                                                        <Lucide name="shield-check" size={18} color="#2F80ED" />
                                                    )}
                                                    <Text style={styles.nameText}>{contact.fullName}</Text>
                                                    <Lucide name="chevron-right" size={16} color="#999" />
{/* 
                                                    {contact?.dealerData?.position && (
                                                        <Text style={styles.positionText}>{'   '}{contact?.dealerData?.position}</Text>
                                                    )} */}

                                                </View>
                                            </View>
                                            <View style={{ flexDirection: 'row', alignItems: 'center', gap: 8, marginRight: 8 }}>
                                                <TouchableOpacity onPress={() => editContact(contact)}>
                                                    <Lucide name="pencil" size={16} color="#999" />
                                                </TouchableOpacity>
                                            </View>
                                        </View>

                                        {/* Contact Info */}
                                        <View style={styles.infoContainer}>

                                            {contact?.dealerData?.position && (
                                                <View style={styles.infoRow}>
                                                    <Lucide name="rectangle-ellipsis" size={16} color="#ccc" />
                                                    <TouchableOpacity onLongPress={() => copyToClipboard(contact.email!, 'Email')}>
                                                        <Text style={styles.infoText}>{contact?.dealerData?.position}</Text>
                                                    </TouchableOpacity>

                                                </View>
                                            )}

                                            {contact.email && (
                                                <View style={styles.infoRow}>
                                                    <Lucide name="mail" size={16} color="#ccc" />
                                                    <TouchableOpacity onLongPress={() => copyToClipboard(contact.email!, 'Email')}>
                                                        <Text style={styles.infoText}>{contact.email}</Text>
                                                    </TouchableOpacity>

                                                </View>
                                            )}

                                            {contact.phone && (
                                                <TouchableOpacity
                                                    style={styles.infoRow}
                                                    onLongPress={() => copyToClipboard(contact.phone!, 'Phone')}
                                                    onPress={() => Linking.openURL(`tel:${contact.phone}`)}
                                                >
                                                    <Lucide name="phone" size={16} color="#ccc" />
                                                    <Text style={styles.infoText}>{contact.phone}</Text>
                                                </TouchableOpacity>
                                            )}
                                        </View>
 
                                    </View>
                                ))}
                            </View>
                        )}
                        {!(dealer?.contacts?.length > 0) && (
                            <Text style={styles.comingSoon}>Henüz kontak eklenmemiş..</Text>
                        )}

                    </View>
                )}
            </Animated.View>

            {/* Modern Contact Form Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={handleCancel}
                statusBarTranslucent={true}
            >
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.modalOverlay}
                    enabled={Platform.OS === 'ios'}
                >
                    <TouchableWithoutFeedback onPress={handleCancel}>
                        <View style={styles.modalBackdrop} />
                    </TouchableWithoutFeedback>

                    <View style={styles.keyboardAvoidingView}>

            <RefreshOverlay
                refreshing={isSaving || isDeleting}
                opacity={refreshOverlayOpacity}
                scale={refreshOverlayScale}
                spinValue={spinValue}
            />

                        <Animated.View
                            style={[
                                styles.modalContainer,
                                {
                                    opacity: modalOpacityAnim,
                                    transform: [{ scale: modalScaleAnim }]
                                }
                            ]}
                        >
                            {/* Modal Header */}
                            <View style={styles.modalHeader}>
                                <View style={styles.modalHeaderContent}>
                                    <View style={styles.modalIconContainer}>
                                        <Lucide name="user-plus" size={24} color="#6B4EFF" />
                                    </View>
                                    <View style={styles.modalTitleContainer}>
                                        <Text style={styles.modalTitle}>{isEditing ? fullName + '' : 'Yeni Kontak Ekle'}</Text>
                                        <Text style={styles.modalSubtitle}>{isEditing ? 'Kontak Bilgilerini düzenle' : 'Bayi için kontak bilgilerini girin'}</Text>
                                    </View>
                                </View>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={handleCancel}
                                    disabled={isSaving}
                                >
                                    <Lucide name="x" size={20} color="#666" />
                                </TouchableOpacity>
                            </View>

                            {/* Modal Content with ScrollView */}
                            <ScrollView
                                style={styles.modalScrollView}
                                showsVerticalScrollIndicator={false}
                                keyboardShouldPersistTaps="handled"
                            >
                                <View style={styles.modalContent}>
                                    {/* Full Name Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Ad Soyad *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="user" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={fullName}
                                                onChangeText={setFullName}
                                                placeholder="Örn: Ahmet Yılmaz"
                                                autoCapitalize="words"
                                                autoCorrect={false}
                                                textContentType="name"
                                                keyboardType="default"
                                                returnKeyType="next"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Position Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Pozisyon *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="briefcase" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={position}
                                                onChangeText={setPosition}
                                                placeholder="Örn: Satış Müdürü"
                                                autoCapitalize="words"
                                                autoCorrect={false}
                                                textContentType="name"
                                                keyboardType="default"
                                                returnKeyType="next"

                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Phone Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>Telefon *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="phone" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={phone}
                                                onChangeText={setPhone}
                                                placeholder="Örn: +90 555 123 45 67"
                                                keyboardType="phone-pad"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Email Input */}
                                    <View style={styles.inputGroup}>
                                        <Text style={styles.inputLabel}>E-posta *</Text>
                                        <View style={styles.inputContainer}>
                                            <Lucide name="mail" size={12} color="#6B4EFF" style={styles.inputIcon} />
                                            <TextInput
                                                style={styles.input}
                                                value={email}
                                                onChangeText={setEmail}
                                                placeholder="Örn: <EMAIL>"
                                                keyboardType="email-address"

                                                autoCorrect={false}
                                                returnKeyType="next"

                                                autoCapitalize="none"
                                                mode="outlined"
                                                outlineColor="#E0E0E0"
                                                activeOutlineColor="#6B4EFF"
                                                disabled={isSaving}
                                                theme={{
                                                    colors: {
                                                        background: '#FAFAFA',
                                                        placeholder: '#999'
                                                    }
                                                }}
                                            />
                                        </View>
                                    </View>

                                    {/* Authorization Checkbox */}

                                    <View style={styles.checkboxContainer}>
                                        <BouncyCheckbox
                                            isChecked={isAuthorized}
                                            onPress={() => setIsAuthorized(!isAuthorized)}
                                            size={25}
                                            disableText={true}
                                            fillColor="red"
                                            unFillColor="#FFFFFF"
                                            // text="Yetkili Kişi Olarak İşaretle"
                                            iconStyle={{ borderColor: "red" }}
                                            innerIconStyle={{ borderWidth: 2 }}
                                        // textStyle={{ fontFamily: "JosefinSans-Regular" }}
                                        />

                                        <TouchableOpacity
                                            onPress={() => setIsAuthorized(!isAuthorized)}
                                            disabled={isSaving}
                                            style={{ marginTop: 4 }}
                                        >
                                            <Text style={styles.checkboxLabel}>
                                                Yetkili kişi olarak işaretle
                                            </Text>
                                        </TouchableOpacity>
                                    </View>

                                </View>
                            </ScrollView>

                            {/* Modal Actions */}
                            <View style={styles.modalActions}>
                                <View>
                                        {isEditing ? <Button
                                            mode="outlined"
                                            onPress={handleDelete}
                                            style={[styles.actionButton, styles.cancelButton, { borderColor: '#dc6408ff', backgroundColor: '#dc6408ff', color: 'white' }]}
                                            labelStyle={[styles.cancelButtonText, { color: 'white' }]}
                                            disabled={isDeleting}
                                        >
                                            Delete
                                        </Button> : <Text> </Text>}
                                </View>
                                <View style={{ flexDirection: 'row', gap: 12 }}>
                                    <Button
                                    mode="outlined"
                                    onPress={handleCancel}
                                    style={[styles.actionButton, styles.cancelButton]}
                                    labelStyle={styles.cancelButtonText}
                                    disabled={isSaving}
                                >
                                    İptal
                                </Button>
                                <Button
                                    mode="contained"
                                    onPress={handleSave}
                                    style={[styles.actionButton, (isSaving || !fullName || !position || !phone || !email) ? { backgroundColor: '#6B4EFF80' } : styles.saveButton]}
                                    labelStyle={[styles.cancelButtonText, { color: 'white' }]}
                                    disabled={isSaving || !fullName || !position || !phone || !email}
                                    loading={isSaving}
                                    // icon={isSaving ? undefined : "check"}
                                    // icon={({ size, color }) => (
                                    //     <Lucide name="check" size={size} color={color || "#fff"} style={styles.checkboxIcon} />
                                    // )}

                                >
                                    {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                </Button>
                                </View>
                            </View>
                        </Animated.View>
                    </View>
                </KeyboardAvoidingView>
            </Modal>
        </>
    )
};

export default RenderContactPeople;
// @ts-ignore
const styles = StyleSheet.create({
    ...stylex.RenderContactPeople, // Merge the two styles objects
    saveButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '700',
    },
}); 