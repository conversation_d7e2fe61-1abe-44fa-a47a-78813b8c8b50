import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Animated } from 'react-native';
import RenderProspectStages from './RenderProspectStages';

// Mock dependencies
jest.mock('../../../../contexts/UserContext', () => ({
    useUser: () => ({
        userInfo: {
            user: { id: '1', name: 'Test User' },
            token: 'test-token'
        }
    })
}));

jest.mock('../../../../lib/fnx.storage.js', () => ({
    AsyncStorage: {
        getItemLimited: jest.fn().mockResolvedValue({
            data: [
                {
                    name: 'adaybayi_asamalar',
                    deger: [
                        { LabelID: 0, LabelCode: "lead", Label: "Aday" },
                        { LabelID: 10, LabelCode: "ilkTemas", Label: "İlk Temas Kuruldu" }
                    ]
                },
                {
                    name: 'adaybayi_durumlar',
                    deger: [
                        { labelID: 0, labelCode: "aktif", label: "Aktif Aday" },
                        { labelID: 100, labelCode: "olumlu", label: "Olumlu" }
                    ]
                }
            ]
        })
    },
    dbLocalTables: {
        entity_dealer_prospects_vars: 'test-table'
    }
}));

jest.mock('../../../../lib/contants.js', () => ({
    apiurls: {
        getdealerprospects: 'http://test.com/api/prospects'
    }
}));

jest.mock('../../../../lib/styles.dealers.js', () => ({
    stylex: {}
}));

describe('RenderProspectStages', () => {
    const mockProps = {
        dealer: {
            id: '1',
            stageCode: 'lead',
            durum: 'aktif',
            refData: 'adaybayi'
        },
        refreshContent: jest.fn(),
        copyToClipboard: jest.fn(),
        fadeAnim: new Animated.Value(1),
        slideAnim: new Animated.Value(0)
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders correctly', () => {
        const { getByText } = render(<RenderProspectStages {...mockProps} />);
        
        expect(getByText('Aday --> Bayi')).toBeTruthy();
    });

    it('shows stage and status chips in header', () => {
        const { getByText } = render(<RenderProspectStages {...mockProps} />);
        
        // Should show current stage and status
        expect(getByText('Aday')).toBeTruthy();
        expect(getByText('Aktif Aday')).toBeTruthy();
    });

    it('expands card content when clicked', () => {
        const { getByText, queryByText } = render(<RenderProspectStages {...mockProps} />);
        
        // Initially collapsed
        expect(queryByText('Güncel Aşama')).toBeFalsy();
        
        // Click to expand
        fireEvent.press(getByText('Aday --> Bayi'));
        
        // Should show expanded content
        expect(getByText('Güncel Aşama')).toBeTruthy();
        expect(getByText('Adaylık Durumu')).toBeTruthy();
        expect(getByText('İşlem Geçmişi')).toBeTruthy();
    });

    it('opens stage modal when stage button is clicked', () => {
        const { getByText, queryByText } = render(<RenderProspectStages {...mockProps} />);
        
        // Expand card first
        fireEvent.press(getByText('Aday --> Bayi'));
        
        // Click stage button
        const stageButtons = getByText('Aday');
        fireEvent.press(stageButtons);
        
        // Should show modal
        expect(getByText('Aşama Güncelle')).toBeTruthy();
    });

    it('opens status modal when status button is clicked', () => {
        const { getByText } = render(<RenderProspectStages {...mockProps} />);
        
        // Expand card first
        fireEvent.press(getByText('Aday --> Bayi'));
        
        // Click status button
        const statusButtons = getByText('Aktif Aday');
        fireEvent.press(statusButtons);
        
        // Should show modal
        expect(getByText('Durum Güncelle')).toBeTruthy();
    });

    it('shows timeline history', () => {
        const { getByText } = render(<RenderProspectStages {...mockProps} />);
        
        // Expand card first
        fireEvent.press(getByText('Aday --> Bayi'));
        
        // Should show timeline items
        expect(getByText('Aşama Güncellendi')).toBeTruthy();
        expect(getByText('Durum Güncellendi')).toBeTruthy();
        expect(getByText('Aday Oluşturuldu')).toBeTruthy();
    });
});
