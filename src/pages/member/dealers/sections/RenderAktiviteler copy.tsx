/* eslint-disable react-native/no-inline-styles */
import React from 'react';
import { View, Text, StyleSheet, Animated, TouchableOpacity, 
    ScrollView, 
    Modal,
    Alert,
    KeyboardAvoidingView,
    TouchableWithoutFeedback,
    Platform,
    Easing,
    ActivityIndicator,
} from 'react-native';
import Lucide from '@react-native-vector-icons/lucide';

import RefreshOverlay from '../List.RefreshOverlay.tsx';
import { useUser } from '../../../../contexts/UserContext.tsx';
import { apiurls } from '../../../../lib/contants.js';
import { TextInput, Button } from 'react-native-paper';

interface RenderAktivitelerProps {
    fadeAnim: Animated.Value;
    slideAnim: Animated.Value;
    dealer?: any;
    refreshContent?: any;
    copyToClipboard: (text: string, label: string) => void;
}

const RenderAktiviteler: React.FC<RenderAktivitelerProps> = ({
    dealer,
    refreshContent,
    copyToClipboard,
    fadeAnim,
    slideAnim
}) => {

    const { userInfo } = useUser();
    const [isCollapsed, setIsCollapsed] = React.useState<boolean>(false);
    // const contactAnimations = React.useRef(new Animated.Value(1)).current;

    // Toggle contact card collapse state
    const toggleContactCollapse = () => {
        const newIsCollapsed = !isCollapsed;
        setIsCollapsed(newIsCollapsed);
    };

    // Animation values for modal
    const modalScaleAnim = React.useRef(new Animated.Value(1)).current;
    const modalOpacityAnim = React.useRef(new Animated.Value(1)).current;
    const [modalVisible, setModalVisible] = React.useState(false);

    const [isSaving, setIsSaving] = React.useState(false);
    // Handle modal open with animation
    const openModal = () => {
        // console.log('Opening modal...', dealer);
        // Set initial animation values for entrance
        modalScaleAnim.setValue(0.8);
        modalOpacityAnim.setValue(0);

        setModalVisible(true);
        // console.log('Modal visible set to true');

        // Start entrance animation immediately
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 1,
                duration: 250,
                useNativeDriver: true,
            })
        ]).start(() => {
            console.log('Animation completed');
        });
    };

    // Handle modal close with animation
    const closeModal = () => {
        Animated.parallel([
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 250,
                useNativeDriver: true,
            }),
            Animated.timing(modalOpacityAnim, {
                toValue: 0,
                duration: 200,
                useNativeDriver: true,
            }),
            Animated.timing(modalScaleAnim, {
                toValue: 0.8,
                duration: 200,
                useNativeDriver: true,
            })
        ]).start(() => {
            setModalVisible(false);
            // Reset form fields
            // setContactId('');
            // setFullName('');
            // setPosition('');
            // setPhone('');
            // setEmail('');
            // setIsAuthorized(false);
            // setIsSaving(false);
            // setIsEditing(false);
        });
    };

    const handleCancel = () => {
        closeModal();
    };

    const handleSave = async () => {
        // Validate required fields
        // if (!fullName || !position || !phone || !email) {
        //     return;
        // }
        let postData = {};

        setIsSaving(true);


        const uri = apiurls.getdealers_contactsXX;
        // console.log('uri', uri);
        try {
            const res = await fetch(uri, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    Authorization: `Bearer ${userInfo?.token}`,
                },
                body: JSON.stringify(postData),
            });
            if (!res.ok) {
                throw new Error(`HTTP error! status: ${res.status}`);
            }
            const data = await res.json();
            // console.log('Contact saved', data);
            // Close modal after saving
            closeModal();
            refreshContent && refreshContent();
        } catch (error) {
            console.log('fetch error', error);
            setIsSaving(false);
            Alert.alert('Hata', 'Kontak kaydedilemedi', [{ text: 'Tamam' }]);
        }


        // Simulate saving with 2-second delay
        // await new Promise((resolve) => setTimeout(() => resolve(null), 2000));


    };

    const [isDeleting, setIsDeleting] = React.useState(false);
    const refreshOverlayOpacity = React.useRef(new Animated.Value(0)).current;
    const refreshOverlayScale = React.useRef(new Animated.Value(0.8)).current;
    const spinValue = React.useRef(new Animated.Value(0)).current;

    React.useEffect(() => {
        if (isSaving || isDeleting) {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 1,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.spring(refreshOverlayScale, {
                    toValue: 1,
                    speed: 10,
                    useNativeDriver: true,
                }),
            ]).start();

            // Start spinning animation
            Animated.loop(
                Animated.timing(spinValue, {
                    toValue: 1,
                    duration: 1000,
                    easing: Easing.linear,
                    useNativeDriver: true,
                })
            ).start();
        } else {
            Animated.parallel([
                Animated.timing(refreshOverlayOpacity, {
                    toValue: 0,
                    duration: 300,
                    useNativeDriver: true,
                }),
                Animated.timing(refreshOverlayScale, {
                    toValue: 0.8,
                    duration: 300,
                    useNativeDriver: true,
                }),
            ]).start();
        }
    }, [isSaving, isDeleting, spinValue]);



    return (
        <>
        <Animated.View style={[styles.card, {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
        }]}>
            <View style={[styles.cardHeader, { justifyContent: 'space-between', alignItems: 'center' }]}>
                <TouchableOpacity style={{ flexDirection: 'row', alignItems: 'center', gap: 2 }}
                    onPress={() => toggleContactCollapse()}
                >
                    <Lucide name="calendar" size={20} color="#6B4EFF" />
                    <Text style={styles.cardTitle}>Aktivite ve Ziyaretler</Text>
                    <Lucide
                        name={!isCollapsed ? "chevron-down" : "chevron-up"}
                        size={20}
                        color="#666"
                    />
                </TouchableOpacity>
                <View>
                    <TouchableOpacity style={styles.addButton} onPress={openModal}>
                        <Lucide name="plus" size={14} color="#6B4EFF" />
                        <Text style={styles.addButtonText}>Yeni</Text>
                    </TouchableOpacity>
                </View>
            </View>
            {/* <TouchableOpacity style={styles.cardHeader} onPress={() => toggleContactCollapse()}>
                <Lucide name="calendar" size={20} color="#6B4EFF" />
                <Text style={styles.cardTitle}>Aktivite ve Ziyaretler</Text>
                <Lucide
                    name={!isCollapsed ? "chevron-down" : "chevron-up"}
                    size={20}
                    color="#666"
                />
            </TouchableOpacity> */}
            {isCollapsed && (
                <View style={styles.cardContent}>
                    <Text style={styles.comingSoon}>Yakında gelecek...</Text>
                </View>
            )}
        </Animated.View>
        
            {/* Modern Contact Form Modal */}
            <Modal
                visible={modalVisible}
                transparent={true}
                animationType="slide"
                onRequestClose={handleCancel}
                statusBarTranslucent={true}
            >
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={styles.modalOverlay}
                    enabled={Platform.OS === 'ios'}
                >
                    <TouchableWithoutFeedback onPress={handleCancel}>
                        <View style={styles.modalBackdrop} />
                    </TouchableWithoutFeedback>

                    <View style={styles.keyboardAvoidingView}>

            <RefreshOverlay
                refreshing={isSaving || isDeleting}
                opacity={refreshOverlayOpacity}
                scale={refreshOverlayScale}
                spinValue={spinValue}
            />

                        <Animated.View
                            style={[
                                styles.modalContainer,
                                {
                                    opacity: modalOpacityAnim,
                                    transform: [{ scale: modalScaleAnim }]
                                }
                            ]}
                        >
                            {/* Modal Header */}
                            <View style={styles.modalHeader}>
                                <View style={styles.modalHeaderContent}>
                                    <View style={styles.modalIconContainer}>
                                        <Lucide name="user-plus" size={24} color="#6B4EFF" />
                                    </View>
                                    <View style={styles.modalTitleContainer}>
                                        <Text style={styles.modalTitle}>{'Yeni Aktivite Ekle'}</Text>
                                        <Text style={styles.modalSubtitle}>{'Bayi için kontak bilgilerini girin'}</Text>
                                    </View>
                                </View>
                                <TouchableOpacity
                                    style={styles.closeButton}
                                    onPress={handleCancel}
                                    disabled={isSaving}
                                >
                                    <Lucide name="x" size={20} color="#666" />
                                </TouchableOpacity>
                            </View>

                            {/* Modal Content with ScrollView */}
                            <ScrollView
                                style={styles.modalScrollView}
                                showsVerticalScrollIndicator={false}
                                keyboardShouldPersistTaps="handled"
                            >
                                <View style={styles.modalContent}>
                                    {/* Full Name Input */}

                                </View>
                            </ScrollView>

                            {/* Modal Actions */}
                            <View style={styles.modalActions}>
                                <View>
                                          <Text> </Text>
                                </View>
                                <View style={{ flexDirection: 'row', gap: 12 }}>
                                    <Button
                                    mode="outlined"
                                    onPress={handleCancel}
                                    style={[styles.actionButton, styles.cancelButton]}
                                    labelStyle={styles.cancelButtonText}
                                    disabled={isSaving}
                                >
                                    İptal
                                </Button>
                                <Button
                                    mode="contained"
                                    onPress={handleSave}
                                    style={[styles.actionButton, (isSaving ? { backgroundColor: '#6B4EFF80' } : styles.saveButton)]}
                                    labelStyle={[styles.cancelButtonText, { color: 'white' }]}
                                    disabled={isSaving}
                                    loading={isSaving}
                                    // icon={isSaving ? undefined : "check"}
                                    // icon={({ size, color }) => (
                                    //     <Lucide name="check" size={size} color={color || "#fff"} style={styles.checkboxIcon} />
                                    // )}

                                >
                                    {isSaving ? 'Kaydediliyor...' : 'Kaydet'}
                                </Button>
                                </View>
                            </View>
                        </Animated.View>
                    </View>
                </KeyboardAvoidingView>
            </Modal>
        </>
    );
};

const styles = StyleSheet.create({

        modalActions: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            padding: 16,
            gap: 12,
            borderTopWidth: 1,
            borderTopColor: '#F0F0F0',
            backgroundColor: '#FAFBFF',
        },
        actionButton: {
            minWidth: 40,
            borderRadius: 10,
            paddingVertical: 2,
        },
        cancelButton: {
            borderColor: '#E0E0E0',
            backgroundColor: 'transparent',
        },
        cancelButtonText: {
            color: '#666',
            fontSize: 14,
            fontWeight: '600',
            marginHorizontal: 8,
            marginVertical: 6,
        },
        saveButton: {
            backgroundColor: '#6B4EFF',
            elevation: 2,
            shadowColor: '#6B4EFF',
            shadowOffset: { width: 0, height: 4 },
            shadowOpacity: 0.3,
            shadowRadius: 8,
        },
        saveButtonText: {
            color: 'white',
            fontSize: 14,
            fontWeight: '700',
            marginHorizontal: 8,
            marginVertical: 6,

        },
    addButton: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
        paddingVertical: 6,
        paddingHorizontal: 12,
        borderRadius: 20,
        backgroundColor: '#F0EDFF',
    },
    addButtonText: {
        fontSize: 12,
        color: '#6B4EFF',
        fontWeight: '500',
    },

    card: {
        backgroundColor: 'white',
        borderRadius: 12,
        marginBottom: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    cardHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        padding: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F0F0F0',
    },
    cardTitle: {
        fontSize: 14,
        fontWeight: '600',
        color: '#333',
        marginLeft: 12,
    },
    cardContent: {
        padding: 16,
        minHeight: 100,
        justifyContent: 'center',
    },
    comingSoon: {
        textAlign: 'center',
        color: '#666',
        fontStyle: 'italic',
    },

        modalOverlay: {
            flex: 1,
            backgroundColor: 'transparent',
        },
        modalBackdrop: {
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.25)',
        },
        keyboardAvoidingView: {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
        },
        modalContainer: {
            width: '95%',
            maxWidth: 450,
            height: '80%',
            backgroundColor: 'white',
            borderRadius: 20,
            overflow: 'hidden',
            shadowColor: '#000',
            shadowOffset: { width: 0, height: 8 },
            shadowOpacity: 0.25,
            shadowRadius: 20,
            elevation: 12,
        },
        modalHeader: {
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            padding: 24,
            borderBottomWidth: 1,
            borderBottomColor: '#F0F0F0',
            backgroundColor: '#FAFBFF',
        },
        modalHeaderContent: {
            flexDirection: 'row',
            alignItems: 'center',
            flex: 1,
        },
        modalIconContainer: {
            width: 36,
            height: 36,
            borderRadius: 18,
            backgroundColor: '#F0EDFF',
            justifyContent: 'center',
            alignItems: 'center',
            marginRight: 16,
        },
        modalTitleContainer: {
            flex: 1,
        },
        modalTitle: {
            fontSize: 16,
            fontWeight: '700',
            color: '#1A1A1A',
            marginBottom: 4,
        },
        modalSubtitle: {
            fontSize: 12,
            color: '#666',
            fontWeight: '400',
        },
        closeButton: {
            width: 36,
            height: 36,
            borderRadius: 18,
            backgroundColor: '#F5F5F5',
            justifyContent: 'center',
            alignItems: 'center',
        },
        modalScrollView: {
            flex: 1,
            backgroundColor: 'white',
        },
        modalContent: {
            padding: 16,
            paddingTop: 16,
        },
});

export default RenderAktiviteler;