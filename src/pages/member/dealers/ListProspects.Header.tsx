import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Header from '../../../navigation/Header';
import SearchBar from './List.SearchBar';
import FilterChips from './List.FilterChips';
import Lucide from '@react-native-vector-icons/lucide';
import ProspectsAddNew from './ProspectsAddNew';

interface ProspectsListHeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  loading: boolean;
  dealerCount: number;
  dealerProspects_vars: any;
  dealerProspectsSegmentSelected: string;
  setdealerProspectsSegmentSelected: (query: string) => void;
}

const ProspectsListHeader: React.FC<ProspectsListHeaderProps> = ({
  searchQuery,
  setSearchQuery,
  loading,
  dealerCount,
  dealerProspects_vars,
  dealerProspectsSegmentSelected,
  setdealerProspectsSegmentSelected,
}) => {
  // console.log('dealerProspects_vars', dealerProspects_vars)
  return (
    <View>
      <Header title="Aday Acenteler" showbackbutton >
        <View style={{ flexDirection: 'row', alignItems: 'center', gap: 6, }}>
          {/* <Lucide name="square-plus" size={28} color="#6B4EFF" />
           */}
           <ProspectsAddNew />
        </View>
      </Header>
      <SearchBar
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
      />
      <FilterChips 
        bayiSegmentleri={dealerProspects_vars?.data?.adaybayi_durumlar} 
        dealerSegmentSelected={dealerProspectsSegmentSelected}
        setDealerSegmentSelected={setdealerProspectsSegmentSelected}
      />
      {!loading && (
        <View style={styles.content}>
          <Text style={styles.dealerCount}>
            {dealerCount} aday .
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  dealerCount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginHorizontal: 16,
    marginVertical: 12,
    textAlign: 'left'
  },
});

export default ProspectsListHeader;