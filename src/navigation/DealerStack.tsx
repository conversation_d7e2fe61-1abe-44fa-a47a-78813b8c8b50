import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import DealersMain from '../pages/member/dealers/DealersMain.tsx';
import DealersHome from '../pages/member/dealers/DealersHome.tsx';
import DealersList from '../pages/member/dealers/DealersList.tsx';
import DealersDetail from '../pages/member/dealers/DealersDetail.tsx';
import AllActivitiesPage from '../pages/member/dealers/AllActivitiesPage.tsx';
import ActivityDetailScreen from '../pages/member/dealers/ActivityDetailScreen.tsx';

import ProspectList from '../pages/member/dealers/ProspectsList.tsx';
import ProspectDetail from '../pages/member/dealers/ProspectsDetail.tsx';

type ProfileStackParamList = {
  DealersMain: undefined;
  DealersHome: undefined;
  DealersList: undefined;
  ProspectList: undefined;
  ProspectDetail: {
    id: string;
    item: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
    };
  };
  DealersDetail: {
    id: string;
    item: {
      id: string;
      tabelaAdi?: string;
      ticariUnvan?: string;
      segment: string;
      durum: string;
      konum: string;
    };
  };
  AllActivitiesPage: {
    activities: any[];
    dealerName: string;
  };
  ActivityDetailScreen: {
    activity: any;
    dealerName: string;
  };
};

const Stack = createStackNavigator<ProfileStackParamList>();

const ProfileStack = () => {
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="DealersMain" component={DealersMain} />
      <Stack.Screen name="DealersHome" component={DealersHome} />
      <Stack.Screen name="DealersList" component={DealersList} />
      <Stack.Screen name="DealersDetail" component={DealersDetail} />
      <Stack.Screen name="AllActivitiesPage" component={AllActivitiesPage} />
      <Stack.Screen name="ActivityDetailScreen" component={ActivityDetailScreen} />

      <Stack.Screen name="ProspectList" component={ProspectList} />
      <Stack.Screen name="ProspectDetail" component={ProspectDetail} />

    </Stack.Navigator>
  );
};

export default ProfileStack;
